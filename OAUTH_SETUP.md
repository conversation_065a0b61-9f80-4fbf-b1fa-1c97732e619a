# OAuth Setup Guide

## Google OAuth Setup

To enable Google sign-in, follow these steps:

### 1. Create a Google Cloud Project

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Navigate to **APIs & Services** > **Credentials**

### 2. Configure OAuth Consent Screen

1. Click **OAuth consent screen**
2. Choose **External** user type
3. Fill in the required information:
   - **App name**: Your SaaS Application Name
   - **User support email**: Your email
   - **Developer contact information**: Your email
4. Add scopes: `email`, `profile`, `openid`
5. Save and continue

### 3. Create OAuth 2.0 Credentials

1. Go to **Credentials** tab
2. Click **Create Credentials** > **OAuth 2.0 Client IDs**
3. Choose **Web application**
4. Add authorized redirect URIs:
   - For development: `http://localhost:5173/api/auth/callback/google`
   - For production: `https://yourdomain.com/api/auth/callback/google`
5. Save the credentials

### 4. Update Environment Variables

Copy the **Client ID** and **Client Secret** from Google and update your `.env` file:

```env
GOOGLE_CLIENT_ID="your-actual-google-client-id"
GOOGLE_CLIENT_SECRET="your-actual-google-client-secret"
```

### 5. GitHub OAuth Setup (Optional)

1. Go to [GitHub Developer Settings](https://github.com/settings/developers)
2. Click **New OAuth App**
3. Fill in:
   - **Application name**: Your SaaS Application Name
   - **Homepage URL**: `http://localhost:5173` (for dev)
   - **Authorization callback URL**: `http://localhost:5173/api/auth/callback/github`
4. Update your `.env` file:

```env
GITHUB_CLIENT_ID="your-github-client-id"
GITHUB_CLIENT_SECRET="your-github-client-secret"
```

## Database Setup

Your Neon database is already configured! Better Auth will automatically create the necessary tables when you first run the app.

## Testing

1. Restart your development server: `bun run dev`
2. Go to `/auth/signin`
3. Click the Google sign-in button
4. You should be redirected to Google's OAuth flow

## Troubleshooting

- **Invalid redirect URI**: Make sure the redirect URI in Google Console matches exactly
- **Client ID not found**: Double-check the environment variables are correct
- **Database errors**: Run database migrations: `bun run db:push`

## Current Status

✅ Auth secret configured  
✅ Database connected (Neon)  
✅ Auth endpoints set up  
⚠️ Google OAuth needs real credentials  
⚠️ GitHub OAuth needs real credentials  