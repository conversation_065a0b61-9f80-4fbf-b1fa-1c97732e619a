{"permissions": {"allow": ["Bash(bun add:*)", "Bash(bun run:*)", "Bash(cp:*)", "Bash(VITE_BUILD_MODE=true bun run build)", "Bash(grep:*)", "Bash(rg:*)", "<PERSON><PERSON>(touch:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true)", "Bash(lsof:*)", "Bash(ss:*)", "Bash(bun test:*)", "Bash(rm:*)", "WebFetch(domain:www.better-auth.com)", "Bash(bunx drizzle-kit push:*)", "Bash(bunx:*)", "Bash(FORCE_MIGRATE=true bun run db:push)", "Bash(ls:*)"], "deny": []}}