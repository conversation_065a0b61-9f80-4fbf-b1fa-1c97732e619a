# Database
DATABASE_URL="***********************************************************"
NEON_DATABASE_URL="******************************************************"

# Authentication (Better Auth)
AUTH_SECRET="your-auth-secret-key-here"
AUTH_TRUST_HOST="true"

# OAuth Providers
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
GITHUB_CLIENT_ID="your-github-client-id"
GITHUB_CLIENT_SECRET="your-github-client-secret"

# Email (for auth verification)
SMTP_HOST="smtp.example.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-email-password"
FROM_EMAIL="<EMAIL>"

# Polar Payments
POLAR_ACCESS_TOKEN="your-polar-access-token"
POLAR_WEBHOOK_SECRET="your-polar-webhook-secret"
POLAR_ORGANIZATION_ID="your-polar-organization-id"

# OpenAI
OPENAI_API_KEY="sk-your-openai-api-key"
OPENAI_ORGANIZATION="your-openai-org-id"

# Inngest
INNGEST_EVENT_KEY="your-inngest-event-key"
INNGEST_SIGNING_KEY="your-inngest-signing-key"

# Application
PUBLIC_APP_URL="http://localhost:5173"
APP_ENV="development"

# Analytics (optional)
GOOGLE_ANALYTICS_ID="G-XXXXXXXXXX"
POSTHOG_KEY="your-posthog-key"

# Error Tracking (optional)
SENTRY_DSN="your-sentry-dsn"

# Redis (for caching, optional)
REDIS_URL="redis://localhost:6379"

# File Storage (optional)
AWS_ACCESS_KEY_ID="your-aws-access-key"
AWS_SECRET_ACCESS_KEY="your-aws-secret-key"
AWS_S3_BUCKET="your-s3-bucket"
AWS_REGION="us-east-1" 