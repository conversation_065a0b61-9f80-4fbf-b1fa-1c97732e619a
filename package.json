{"name": "sveltekit-buildings", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "db:push": "drizzle-kit push", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio"}, "devDependencies": {"@internationalized/date": "^3.8.1", "@lucide/svelte": "^0.511.0", "@sveltejs/adapter-auto": "^6.0.0", "@sveltejs/kit": "^2.16.0", "@sveltejs/vite-plugin-svelte": "^5.0.0", "@tailwindcss/vite": "^4.0.0", "@types/bcryptjs": "^3.0.0", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^22", "bits-ui": "^2.4.1", "cmdk-sv": "^0.0.19", "drizzle-kit": "^0.30.2", "embla-carousel-svelte": "^8.6.0", "formsnap": "1.0.1", "lucide-svelte": "^0.513.0", "mode-watcher": "^1.0.7", "paneforge": "^0.0.6", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "svelte-sonner": "^1.0.4", "sveltekit-superforms": "^2.26.0", "tailwind-variants": "^1.0.0", "tailwindcss": "^4.0.0", "typescript": "^5.0.0", "vaul-svelte": "^0.3.2", "vite": "^6.2.6", "zod": "^3.25.51"}, "dependencies": {"@auth/drizzle-adapter": "^1.9.1", "@auth/sveltekit": "^1.9.2", "@hookform/resolvers": "^5.0.1", "@neondatabase/serverless": "^0.10.4", "@paralleldrive/cuid2": "^2.2.2", "@polar-sh/sdk": "^0.33.0", "@tanstack/svelte-query": "^5.80.2", "@trpc/client": "^11.2.0", "@trpc/server": "^11.2.0", "@upstash/ratelimit": "^2.0.5", "@upstash/redis": "^1.35.0", "better-auth": "^1.2.8", "clsx": "^2.1.1", "drizzle-orm": "^0.40.0", "inngest": "^3.38.0", "openai": "^5.1.0", "tailwind-merge": "^3.3.0", "trpc-sveltekit": "^3.6.3"}}