# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a production-ready SaaS starter built with SvelteKit, targeting modern web application development with a complete tech stack for authentication, payments, AI integration, and background jobs.

## Development Commands

### Core Development
- `bun run dev` - Start development server
- `bun run build` - Build for production  
- `bun run preview` - Preview production build
- `bun run check` - Run TypeScript checks and Svelte validation
- `bun run check:watch` - Run checks in watch mode

### Database Operations
- `bun run db:push` - Push schema changes to database
- `bun run db:migrate` - Run database migrations
- `bun run db:studio` - Open Drizzle Studio for database management

### Package Management
- Uses **Bun** as runtime and package manager (not npm/yarn)
- Dependencies managed through bun.lock

## Architecture & Tech Stack

### Core Framework
- **SvelteKit** with Svelte 5.0 and TypeScript
- **Vite** for build tooling with auto adapter
- **Tailwind CSS v4** with custom CSS properties theming

### Database Layer
- **Neon PostgreSQL** (cloud-native PostgreSQL)
- **Drizzle ORM** with schema-first approach in `src/lib/server/db/schema.ts`
- Currently minimal schema (just user table) - needs expansion per tasks.md

### UI Components
- **shadcn/ui** with 40+ pre-installed components
- **bits-ui** as the underlying primitive library
- **Dark/light mode** via mode-watcher (defaults to dark)
- **Responsive sidebar layout** with collapsible navigation

### Authentication & API
- **Better Auth** with Drizzle adapter (not yet implemented)
- **tRPC** for type-safe API layer (not yet implemented)
- **Zod** for schema validation

### External Integrations
- **Polar** for payment processing
- **OpenAI API** for AI features
- **Inngest** for background jobs
- **@tanstack/svelte-query** for data fetching

## Key Architectural Patterns

### Component Structure
- All UI components follow shadcn/ui patterns with TypeScript
- Components export through index.ts files
- Sidebar-based layout with breadcrumb navigation

### Database Strategy
- Schema defined in `src/lib/server/db/schema.ts`
- Drizzle migrations managed through drizzle-kit
- Currently using Neon serverless driver

### Styling System
- CSS custom properties for theming (see tailwind.config.ts)
- Dark mode as default with toggle capability
- Responsive design with mobile-first approach

## Current Development State

### ✅ Complete Infrastructure
- SvelteKit project with all core dependencies
- UI component library fully installed
- Theme system and sidebar navigation working
- Build system and development environment ready

### 🏗️ Needs Implementation (Priority Order)
1. **Database schema expansion** - Currently only has basic user table
2. **Better Auth setup** - Authentication flows not implemented
3. **tRPC API structure** - No API routes yet
4. **Polar payment integration** - Payment system not connected
5. **Background job setup** - Inngest not configured

### ⚠️ Known Issues
- 28 non-critical TypeScript errors from shadcn/bits-ui compatibility
- These are cosmetic type issues that don't affect functionality
- Application builds and runs perfectly despite these errors

## Environment Setup

Required environment variables (from env.example):
- `DATABASE_URL` - Neon PostgreSQL connection string
- `AUTH_SECRET` - Better Auth secret key
- `GITHUB_CLIENT_ID/SECRET` - OAuth credentials
- `POLAR_ACCESS_TOKEN` - Payment processing
- `OPENAI_API_KEY` - AI integration
- `INNGEST_EVENT_KEY/SIGNING_KEY` - Background jobs

## Development Guidelines

### Code Standards
- TypeScript strict mode enabled
- Follow existing shadcn/ui component patterns
- Maintain responsive design principles
- Use Zod for input validation

### Database Operations
- Use Drizzle ORM for all database operations
- Schema changes go through drizzle-kit migrations
- Test schema changes with `bun run db:studio`

### API Development
- When implementing tRPC, follow type-safe patterns
- Use Zod schemas for input validation
- Implement proper error handling

## File Structure Notes

### Key Directories
- `src/lib/components/ui/` - shadcn/ui components (extensive library)
- `src/lib/server/db/` - Database configuration and schema
- `src/routes/` - SvelteKit file-based routing
- `src/lib/components/` - Custom app components (nav, sidebar, etc.)

### Important Files
- `drizzle.config.ts` - Database configuration
- `tailwind.config.ts` - Theme and styling configuration
- `tasks.md` - Complete 6-phase production roadmap
- `src/routes/+layout.svelte` - Main app layout with sidebar

## Next Development Steps

Reference `tasks.md` for the complete roadmap. Immediate priorities:
1. Expand database schema beyond basic user table
2. Implement Better Auth authentication flows  
3. Set up tRPC API structure
4. Configure environment variables properly
5. Begin Polar payment integration

The project is well-structured with excellent tooling and a clear path to production.