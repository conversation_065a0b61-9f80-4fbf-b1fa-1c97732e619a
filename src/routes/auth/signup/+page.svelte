<script lang="ts">
	import { signUp, signIn } from "$lib/auth/client";
	import { Button } from "$lib/components/ui/button";
	import { Input } from "$lib/components/ui/input";
	import { Label } from "$lib/components/ui/label";
	import * as Card from "$lib/components/ui/card";
	import { Separator } from "$lib/components/ui/separator";
	import { Github } from "lucide-svelte";
	import { goto } from "$app/navigation";
	import { page } from "$app/stores";

	let name = "";
	let email = "";
	let password = "";
	let confirmPassword = "";
	let loading = false;
	let error = "";
	let success = "";

	async function handleEmailSignUp() {
		if (!name || !email || !password || !confirmPassword) {
			error = "Please fill in all fields";
			return;
		}

		if (password !== confirmPassword) {
			error = "Passwords do not match";
			return;
		}

		if (password.length < 8) {
			error = "Password must be at least 8 characters long";
			return;
		}

		loading = true;
		error = "";
		success = "";

		try {
			const result = await signUp.email({
				name,
				email,
				password
			});

			if (result.error) {
				error = result.error.message || "Sign up failed";
			} else {
				success = "Account created! Please check your email to verify your account.";
				// Optionally redirect after a delay
				setTimeout(() => {
					goto("/auth/signin");
				}, 3000);
			}
		} catch (e) {
			error = "An unexpected error occurred";
		} finally {
			loading = false;
		}
	}

	async function handleGitHubSignUp() {
		loading = true;
		error = "";
		try {
			await signIn.social({
				provider: "github"
			});
		} catch (e) {
			console.error("GitHub sign up error:", e);
			error = "GitHub sign up failed. Please try again.";
			loading = false;
		}
	}

	async function handleGoogleSignUp() {
		loading = true;
		error = "";
		try {
			await signIn.social({
				provider: "google"
			});
		} catch (e) {
			console.error("Google sign up error:", e);
			error = "Google sign up failed. Please try again.";
			loading = false;
		}
	}
</script>

<svelte:head>
	<title>Sign Up - Buildings App</title>
</svelte:head>

<div class="flex min-h-screen items-center justify-center">
	<Card.Root class="w-full max-w-md">
		<Card.Header class="space-y-1">
			<Card.Title class="text-2xl font-bold">Create Account</Card.Title>
			<Card.Description>
				Enter your information to create a new account
			</Card.Description>
		</Card.Header>
		<Card.Content class="space-y-4">
			{#if error}
				<div class="rounded-md bg-destructive/15 p-3 text-sm text-destructive">
					{error}
				</div>
			{/if}

			{#if success}
				<div class="rounded-md bg-green-50 p-3 text-sm text-green-800 dark:bg-green-900/20 dark:text-green-400">
					{success}
				</div>
			{/if}

			<form on:submit|preventDefault={handleEmailSignUp} class="space-y-4">
				<div class="space-y-2">
					<Label for="name">Full Name</Label>
					<Input
						id="name"
						type="text"
						placeholder="Enter your full name"
						bind:value={name}
						disabled={loading}
						required
					/>
				</div>
				<div class="space-y-2">
					<Label for="email">Email</Label>
					<Input
						id="email"
						type="email"
						placeholder="Enter your email"
						bind:value={email}
						disabled={loading}
						required
					/>
				</div>
				<div class="space-y-2">
					<Label for="password">Password</Label>
					<Input
						id="password"
						type="password"
						placeholder="Create a password"
						bind:value={password}
						disabled={loading}
						required
					/>
				</div>
				<div class="space-y-2">
					<Label for="confirm-password">Confirm Password</Label>
					<Input
						id="confirm-password"
						type="password"
						placeholder="Confirm your password"
						bind:value={confirmPassword}
						disabled={loading}
						required
					/>
				</div>
				<Button type="submit" class="w-full" disabled={loading}>
					{loading ? "Creating account..." : "Create Account"}
				</Button>
			</form>

			<div class="relative">
				<div class="absolute inset-0 flex items-center">
					<Separator class="w-full" />
				</div>
				<div class="relative flex justify-center text-xs uppercase">
					<span class="bg-background px-2 text-muted-foreground">Or continue with</span>
				</div>
			</div>

			<div class="grid grid-cols-2 gap-2">
				<Button variant="outline" disabled={loading} onclick={handleGitHubSignUp}>
					<Github class="mr-2 h-4 w-4" />
					GitHub
				</Button>
				<Button variant="outline" disabled={loading} onclick={handleGoogleSignUp}>
					<svg class="mr-2 h-4 w-4" viewBox="0 0 24 24">
						<path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
						<path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
						<path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
						<path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
					</svg>
					Google
				</Button>
			</div>

			<div class="text-center text-sm">
				Already have an account?{" "}
				<a href="/auth/signin" class="font-medium text-primary underline-offset-4 hover:underline">
					Sign in
				</a>
			</div>
		</Card.Content>
	</Card.Root>
</div>