<script lang="ts">
	import { But<PERSON> } from '$lib/components/ui/button/index.js';
	import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '$lib/components/ui/card/index.js';
	import { Badge } from '$lib/components/ui/badge/index.js';
	import { Avatar, AvatarFallback, AvatarImage } from '$lib/components/ui/avatar/index.js';
	import { Skeleton } from '$lib/components/ui/skeleton/index.js';
	import { Alert, AlertDescription } from '$lib/components/ui/alert/index.js';
	import { Progress } from '$lib/components/ui/progress/index.js';
	import { onMount } from 'svelte';
	import type { PageData } from './$types';
	import { 
		User, 
		Settings, 
		CreditCard, 
		Users,
		Activity,
		Bell,
		Calendar,
		TrendingUp,
		Shield,
		Clock,
		Building
	} from 'lucide-svelte';

	export let data: PageData;

	let userProfile: any = null;
	let userStats: any = null;
	let userOrganizations: any = null;
	let recentActivity: any = null;
	let loading = true;
	let error: string | null = null;

	onMount(async () => {
		if (data.user) {
			try {
				const [profile, stats] = await Promise.all([
					data.trpc.users.me.query(),
					data.trpc.users.stats.query()
				]);
				
				userProfile = profile;
				userStats = stats;
			} catch (err) {
				console.error('Dashboard data loading error:', err);
				error = err instanceof Error ? err.message : 'Failed to load dashboard data';
			}
		}
		loading = false;
	});

	function getUserInitials(name: string | null | undefined): string {
		if (!name) return 'U';
		return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
	}

	function formatDate(date: Date | string | null | undefined): string {
		if (!date) return 'Never';
		return new Date(date).toLocaleDateString('en-US', {
			year: 'numeric',
			month: 'short',
			day: 'numeric'
		});
	}

	function getTimeAgo(date: Date | string | null | undefined): string {
		if (!date) return 'Never';
		const now = new Date();
		const past = new Date(date);
		const diffInSeconds = Math.floor((now.getTime() - past.getTime()) / 1000);
		
		if (diffInSeconds < 60) return 'Just now';
		if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
		if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
		return `${Math.floor(diffInSeconds / 86400)}d ago`;
	}
</script>

<div class="space-y-6">
	<!-- Header Section -->
	<div class="flex flex-col gap-2">
		<div class="flex items-center justify-between">
			<div>
				<h1 class="text-3xl font-bold tracking-tight">Dashboard</h1>
				<p class="text-muted-foreground">
					Welcome back{userProfile?.name ? `, ${userProfile.name}` : ''}
				</p>
			</div>
			{#if userProfile}
				<div class="flex items-center gap-2">
					<Badge variant={userProfile.role === 'admin' ? 'default' : 'secondary'}>
						{userProfile.role === 'admin' ? 'Admin' : 'User'}
					</Badge>
					<Avatar class="h-10 w-10">
						<AvatarImage src={userProfile.image || ''} alt={userProfile.name || 'User'} />
						<AvatarFallback>{getUserInitials(userProfile.name)}</AvatarFallback>
					</Avatar>
				</div>
			{/if}
		</div>
	</div>

	{#if error}
		<Alert variant="destructive">
			<AlertDescription>{error}</AlertDescription>
		</Alert>
	{/if}

	<!-- Stats Overview -->
	<div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
		{#if loading}
			{#each Array(4) as _}
				<Card>
					<CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
						<Skeleton class="h-4 w-24" />
						<Skeleton class="h-4 w-4" />
					</CardHeader>
					<CardContent>
						<Skeleton class="h-7 w-16 mb-1" />
						<Skeleton class="h-3 w-32" />
					</CardContent>
				</Card>
			{/each}
		{:else if userProfile}
			<Card>
				<CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
					<CardTitle class="text-sm font-medium">Account Status</CardTitle>
					<Shield class="h-4 w-4 text-muted-foreground" />
				</CardHeader>
				<CardContent>
					<div class="text-2xl font-bold">
						{userProfile.isActive ? 'Active' : 'Inactive'}
					</div>
					<p class="text-xs text-muted-foreground">
						Since {formatDate(userProfile.createdAt)}
					</p>
				</CardContent>
			</Card>

			<Card>
				<CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
					<CardTitle class="text-sm font-medium">Last Login</CardTitle>
					<Clock class="h-4 w-4 text-muted-foreground" />
				</CardHeader>
				<CardContent>
					<div class="text-2xl font-bold">
						{getTimeAgo(userProfile.lastLoginAt)}
					</div>
					<p class="text-xs text-muted-foreground">
						{formatDate(userProfile.lastLoginAt)}
					</p>
				</CardContent>
			</Card>

			<Card>
				<CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
					<CardTitle class="text-sm font-medium">Total Users</CardTitle>
					<Users class="h-4 w-4 text-muted-foreground" />
				</CardHeader>
				<CardContent>
					<div class="text-2xl font-bold">
						{userStats?.totalUsers || 0}
					</div>
					<p class="text-xs text-muted-foreground">
						{userStats?.totalAdmins || 0} administrators
					</p>
				</CardContent>
			</Card>

			<Card>
				<CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
					<CardTitle class="text-sm font-medium">Profile Status</CardTitle>
					<TrendingUp class="h-4 w-4 text-muted-foreground" />
				</CardHeader>
				<CardContent>
					<div class="text-2xl font-bold">
						{userProfile.emailVerified ? '100%' : '50%'}
					</div>
					<p class="text-xs text-muted-foreground">
						Profile completion
					</p>
				</CardContent>
			</Card>
		{/if}
	</div>

	<div class="grid gap-4 lg:grid-cols-2">
		<!-- User Profile Card -->
		<Card>
			<CardHeader>
				<CardTitle class="flex items-center gap-2">
					<User class="h-5 w-5" />
					Profile Overview
				</CardTitle>
				<CardDescription>Your account information and settings</CardDescription>
			</CardHeader>
			<CardContent class="space-y-4">
				{#if loading}
					<div class="space-y-2">
						<Skeleton class="h-4 w-20" />
						<Skeleton class="h-4 w-40" />
					</div>
					<div class="space-y-2">
						<Skeleton class="h-4 w-16" />
						<Skeleton class="h-4 w-32" />
					</div>
				{:else if userProfile}
					<div class="space-y-3">
						<div>
							<dt class="text-sm font-medium text-muted-foreground">Email</dt>
							<dd class="text-sm">{userProfile.email}</dd>
						</div>
						<div>
							<dt class="text-sm font-medium text-muted-foreground">Name</dt>
							<dd class="text-sm">{userProfile.name || 'Not set'}</dd>
						</div>
						<div>
							<dt class="text-sm font-medium text-muted-foreground">Role</dt>
							<dd class="text-sm">
								<Badge variant={userProfile.role === 'admin' ? 'default' : 'secondary'}>
									{userProfile.role}
								</Badge>
							</dd>
						</div>
						<div>
							<dt class="text-sm font-medium text-muted-foreground">Email Verified</dt>
							<dd class="text-sm">
								<Badge variant={userProfile.emailVerified ? 'default' : 'destructive'}>
									{userProfile.emailVerified ? 'Verified' : 'Unverified'}
								</Badge>
							</dd>
						</div>
					</div>
					<div class="pt-4 border-t">
						<Button class="w-full" variant="outline" href="/account/profile">
							<Settings class="h-4 w-4 mr-2" />
							Edit Profile
						</Button>
					</div>
				{/if}
			</CardContent>
		</Card>

		<!-- Quick Actions Card -->
		<Card>
			<CardHeader>
				<CardTitle class="flex items-center gap-2">
					<Activity class="h-5 w-5" />
					Quick Actions
				</CardTitle>
				<CardDescription>Common tasks and navigation</CardDescription>
			</CardHeader>
			<CardContent class="grid gap-2">
				<Button variant="outline" class="justify-start" href="/organizations">
					<Building class="h-4 w-4 mr-2" />
					Manage Organizations
				</Button>
				<Button variant="outline" class="justify-start" href="/billing">
					<CreditCard class="h-4 w-4 mr-2" />
					Billing & Subscriptions
				</Button>
				<Button variant="outline" class="justify-start" href="/account/notifications">
					<Bell class="h-4 w-4 mr-2" />
					Notifications
				</Button>
				{#if userProfile?.role === 'admin'}
					<Button variant="outline" class="justify-start" href="/admin">
						<Users class="h-4 w-4 mr-2" />
						Admin Panel
					</Button>
				{/if}
				<Button class="justify-start" href="/organizations">
					<Calendar class="h-4 w-4 mr-2" />
					Get Started
				</Button>
			</CardContent>
		</Card>
	</div>

	<!-- Welcome Section for New Users -->
	{#if userProfile && !userProfile.lastLoginAt}
		<Card class="border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-950">
			<CardHeader>
				<CardTitle class="text-blue-900 dark:text-blue-100">Welcome to the Platform!</CardTitle>
				<CardDescription class="text-blue-700 dark:text-blue-300">
					This appears to be your first time logging in. Here are some next steps to get you started.
				</CardDescription>
			</CardHeader>
			<CardContent class="space-y-4">
				<div class="space-y-2">
					<div class="flex items-center justify-between text-sm">
						<span>Complete your profile</span>
						<span class="text-muted-foreground">{userProfile.name ? '✓' : '○'}</span>
					</div>
					<div class="flex items-center justify-between text-sm">
						<span>Verify your email</span>
						<span class="text-muted-foreground">{userProfile.emailVerified ? '✓' : '○'}</span>
					</div>
					<div class="flex items-center justify-between text-sm">
						<span>Explore the dashboard</span>
						<span class="text-muted-foreground">○</span>
					</div>
				</div>
				<Progress value={33 + (userProfile.name ? 33 : 0) + (userProfile.emailVerified ? 34 : 0)} class="w-full" />
				<Button class="w-full">
					Complete Setup
				</Button>
			</CardContent>
		</Card>
	{/if}

	<!-- System Status -->
	<Card>
		<CardHeader>
			<CardTitle>System Information</CardTitle>
			<CardDescription>Application status and infrastructure details</CardDescription>
		</CardHeader>
		<CardContent class="space-y-4">
			<div class="grid gap-4 md:grid-cols-2">
				<div class="space-y-2">
					<h4 class="text-sm font-medium">Infrastructure Status</h4>
					<div class="space-y-1 text-sm">
						<div class="flex justify-between">
							<span>Database</span>
							<Badge variant="default">Connected</Badge>
						</div>
						<div class="flex justify-between">
							<span>Authentication</span>
							<Badge variant="default">Active</Badge>
						</div>
						<div class="flex justify-between">
							<span>API Layer</span>
							<Badge variant="default">Operational</Badge>
						</div>
					</div>
				</div>
				<div class="space-y-2">
					<h4 class="text-sm font-medium">Tech Stack</h4>
					<div class="text-xs text-muted-foreground space-y-1">
						<div>Framework: SvelteKit + TypeScript</div>
						<div>Database: Neon PostgreSQL + Drizzle ORM</div>
						<div>Auth: Better Auth with session management</div>
						<div>UI: shadcn/ui + Tailwind CSS</div>
						<div>API: tRPC for type-safe endpoints</div>
					</div>
				</div>
			</div>
		</CardContent>
	</Card>
</div>
