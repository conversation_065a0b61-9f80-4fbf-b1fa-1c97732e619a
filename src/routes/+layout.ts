import { createTRPCClient, httpBatchLink } from '@trpc/client';
import type { AppRouter } from '$lib/server/trpc/root';
import { browser } from '$app/environment';
import type { LayoutLoad } from './$types';

export const load: LayoutLoad = async ({ fetch, data }) => {
	const trpc = createTRPCClient<AppRouter>({
		links: [
			httpBatchLink({
				url: browser ? '/api/trpc' : 'http://localhost:5173/api/trpc',
				fetch
			})
		]
	});

	return {
		trpc,
		...data
	};
};