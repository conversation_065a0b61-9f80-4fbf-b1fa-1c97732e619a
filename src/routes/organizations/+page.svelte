<script lang="ts">
	import { But<PERSON> } from '$lib/components/ui/button/index.js';
	import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '$lib/components/ui/card/index.js';
	import { Badge } from '$lib/components/ui/badge/index.js';
	import { Avatar, AvatarFallback, AvatarImage } from '$lib/components/ui/avatar/index.js';
	import { Skeleton } from '$lib/components/ui/skeleton/index.js';
	import { Alert, AlertDescription } from '$lib/components/ui/alert/index.js';
	import { Input } from '$lib/components/ui/input/index.js';
	import { Label } from '$lib/components/ui/label/index.js';
	import { Textarea } from '$lib/components/ui/textarea/index.js';
	import * as Dialog from '$lib/components/ui/dialog/index.js';
	import * as Table from '$lib/components/ui/table/index.js';
	import { onMount } from 'svelte';
	import type { PageData } from './$types';
	import { 
		Building, 
		Plus, 
		Users,
		Settings,
		Crown,
		Shield,
		User,
		Mail,
		MoreHorizontal,
		Calendar,
		Edit,
		Trash,
		UserPlus
	} from 'lucide-svelte';

	export let data: PageData;

	let organizations: any[] = [];
	let selectedOrg: any = null;
	let orgMembers: any[] = [];
	let loading = true;
	let membersLoading = false;
	let error: string | null = null;
	let createDialogOpen = false;
	let inviteDialogOpen = false;

	// Form states
	let createForm = {
		name: '',
		slug: '',
		description: ''
	};
	let inviteForm = {
		email: '',
		role: 'member' as 'member' | 'admin'
	};

	onMount(async () => {
		if (data.user) {
			await loadOrganizations();
		}
		loading = false;
	});

	async function loadOrganizations() {
		try {
			organizations = await data.trpc.organizations.mine.query();
			if (organizations.length > 0 && !selectedOrg) {
				selectedOrg = organizations[0];
				await loadMembers();
			}
		} catch (err) {
			console.error('Failed to load organizations:', err);
			error = err instanceof Error ? err.message : 'Failed to load organizations';
		}
	}

	async function loadMembers() {
		if (!selectedOrg) return;
		
		membersLoading = true;
		try {
			orgMembers = await data.trpc.organizations.members.query({ 
				organizationId: selectedOrg.id 
			});
		} catch (err) {
			console.error('Failed to load members:', err);
		}
		membersLoading = false;
	}

	async function createOrganization() {
		try {
			await data.trpc.organizations.create.mutate(createForm);
			createDialogOpen = false;
			createForm = { name: '', slug: '', description: '' };
			await loadOrganizations();
		} catch (err) {
			error = err instanceof Error ? err.message : 'Failed to create organization';
		}
	}

	async function inviteUser() {
		if (!selectedOrg) return;
		
		try {
			await data.trpc.organizations.inviteUser.mutate({
				organizationId: selectedOrg.id,
				...inviteForm
			});
			inviteDialogOpen = false;
			inviteForm = { email: '', role: 'member' };
			await loadMembers();
		} catch (err) {
			error = err instanceof Error ? err.message : 'Failed to invite user';
		}
	}

	async function removeMember(memberId: string) {
		if (!selectedOrg) return;
		
		try {
			await data.trpc.organizations.removeMember.mutate({
				organizationId: selectedOrg.id,
				memberId
			});
			await loadMembers();
		} catch (err) {
			error = err instanceof Error ? err.message : 'Failed to remove member';
		}
	}

	function getUserInitials(name: string | null | undefined): string {
		if (!name) return 'U';
		return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
	}

	function formatDate(date: Date | string): string {
		return new Date(date).toLocaleDateString('en-US', {
			year: 'numeric',
			month: 'short',
			day: 'numeric'
		});
	}

	function getRoleBadgeVariant(role: string) {
		switch (role) {
			case 'owner': return 'default';
			case 'admin': return 'secondary';
			case 'member': return 'outline';
			default: return 'outline';
		}
	}

	function getRoleIcon(role: string) {
		switch (role) {
			case 'owner': return Crown;
			case 'admin': return Shield;
			case 'member': return User;
			default: return User;
		}
	}

	// Auto-generate slug from name
	$: if (createForm.name) {
		createForm.slug = createForm.name
			.toLowerCase()
			.replace(/[^a-z0-9\s-]/g, '')
			.replace(/\s+/g, '-')
			.replace(/-+/g, '-')
			.trim();
	}
</script>

<div class="space-y-6">
	<!-- Header -->
	<div class="flex items-center justify-between">
		<div>
			<h1 class="text-3xl font-bold tracking-tight">Organizations</h1>
			<p class="text-muted-foreground">
				Manage your teams and collaborate with others
			</p>
		</div>
		<Button on:click={() => createDialogOpen = true}>
			<Plus class="h-4 w-4 mr-2" />
			Create Organization
		</Button>
	</div>

	{#if error}
		<Alert variant="destructive">
			<AlertDescription>{error}</AlertDescription>
		</Alert>
	{/if}

	<div class="grid gap-6 lg:grid-cols-3">
		<!-- Organizations List -->
		<div class="lg:col-span-1">
			<Card>
				<CardHeader>
					<CardTitle class="flex items-center gap-2">
						<Building class="h-5 w-5" />
						Your Organizations
					</CardTitle>
					<CardDescription>
						{organizations.length} organization{organizations.length !== 1 ? 's' : ''}
					</CardDescription>
				</CardHeader>
				<CardContent class="space-y-2">
					{#if loading}
						{#each Array(3) as _}
							<div class="p-3 border rounded-lg space-y-2">
								<Skeleton class="h-4 w-32" />
								<Skeleton class="h-3 w-20" />
							</div>
						{/each}
					{:else if organizations.length === 0}
						<div class="text-center py-8 text-muted-foreground">
							<Building class="h-8 w-8 mx-auto mb-2 opacity-50" />
							<p class="text-sm">No organizations yet</p>
							<Button 
								variant="outline" 
								size="sm" 
								class="mt-2"
								on:click={() => createDialogOpen = true}
							>
								Create your first organization
							</Button>
						</div>
					{:else}
						{#each organizations as org}
							<button
								class="w-full p-3 border rounded-lg text-left hover:bg-muted/50 transition-colors {selectedOrg?.id === org.id ? 'border-primary bg-primary/5' : ''}"
								on:click={() => {
									selectedOrg = org;
									loadMembers();
								}}
							>
								<div class="flex items-start justify-between">
									<div class="flex-1 min-w-0">
										<p class="font-medium truncate">{org.name}</p>
										<p class="text-sm text-muted-foreground truncate">
											{org.slug}
										</p>
										<div class="flex items-center gap-1 mt-1">
											<Badge variant={getRoleBadgeVariant(org.role)} class="text-xs">
												{org.role}
											</Badge>
										</div>
									</div>
								</div>
							</button>
						{/each}
					{/if}
				</CardContent>
			</Card>
		</div>

		<!-- Organization Details & Members -->
		<div class="lg:col-span-2 space-y-6">
			{#if selectedOrg}
				<!-- Organization Info -->
				<Card>
					<CardHeader>
						<div class="flex items-start justify-between">
							<div class="flex items-start gap-3">
								<Avatar class="h-12 w-12">
									<AvatarImage src={selectedOrg.image || ''} alt={selectedOrg.name} />
									<AvatarFallback>{getUserInitials(selectedOrg.name)}</AvatarFallback>
								</Avatar>
								<div>
									<CardTitle class="flex items-center gap-2">
										{selectedOrg.name}
										<Badge variant={getRoleBadgeVariant(selectedOrg.role)}>
											{selectedOrg.role}
										</Badge>
									</CardTitle>
									<CardDescription>
										{selectedOrg.description || 'No description provided'}
									</CardDescription>
									<p class="text-sm text-muted-foreground mt-1">
										Created {formatDate(selectedOrg.createdAt)}
									</p>
								</div>
							</div>
							{#if selectedOrg.role === 'owner' || selectedOrg.role === 'admin'}
								<Button variant="outline" size="sm">
									<Settings class="h-4 w-4 mr-2" />
									Settings
								</Button>
							{/if}
						</div>
					</CardHeader>
				</Card>

				<!-- Members -->
				<Card>
					<CardHeader>
						<div class="flex items-center justify-between">
							<div>
								<CardTitle class="flex items-center gap-2">
									<Users class="h-5 w-5" />
									Members
								</CardTitle>
								<CardDescription>
									Manage team members and their roles
								</CardDescription>
							</div>
							{#if selectedOrg.role === 'owner' || selectedOrg.role === 'admin'}
								<Button on:click={() => inviteDialogOpen = true}>
									<UserPlus class="h-4 w-4 mr-2" />
									Invite User
								</Button>
							{/if}
						</div>
					</CardHeader>
					<CardContent>
						{#if membersLoading}
							<div class="space-y-2">
								{#each Array(3) as _}
									<div class="flex items-center gap-3 p-2">
										<Skeleton class="h-8 w-8 rounded-full" />
										<div class="space-y-1">
											<Skeleton class="h-4 w-32" />
											<Skeleton class="h-3 w-24" />
										</div>
									</div>
								{/each}
							</div>
						{:else if orgMembers.length === 0}
							<div class="text-center py-8 text-muted-foreground">
								<Users class="h-8 w-8 mx-auto mb-2 opacity-50" />
								<p class="text-sm">No members found</p>
							</div>
						{:else}
							<Table.Root>
								<Table.Header>
									<Table.Row>
										<Table.Head>Member</Table.Head>
										<Table.Head>Role</Table.Head>
										<Table.Head>Joined</Table.Head>
										<Table.Head class="w-12"></Table.Head>
									</Table.Row>
								</Table.Header>
								<Table.Body>
									{#each orgMembers as member}
										<Table.Row>
											<Table.Cell>
												<div class="flex items-center gap-3">
													<Avatar class="h-8 w-8">
														<AvatarImage src={member.user.image || ''} alt={member.user.name || 'User'} />
														<AvatarFallback>{getUserInitials(member.user.name)}</AvatarFallback>
													</Avatar>
													<div>
														<p class="font-medium">{member.user.name || 'Unnamed User'}</p>
														<p class="text-sm text-muted-foreground">{member.user.email}</p>
													</div>
												</div>
											</Table.Cell>
											<Table.Cell>
												<Badge variant={getRoleBadgeVariant(member.role)} class="flex items-center gap-1 w-fit">
													<svelte:component this={getRoleIcon(member.role)} class="h-3 w-3" />
													{member.role}
												</Badge>
											</Table.Cell>
											<Table.Cell class="text-muted-foreground">
												{formatDate(member.joinedAt)}
											</Table.Cell>
											<Table.Cell>
												{#if (selectedOrg.role === 'owner' || selectedOrg.role === 'admin') && member.role !== 'owner' && member.user.id !== data.user?.id}
													<Button 
														variant="ghost" 
														size="sm"
														on:click={() => removeMember(member.id)}
														class="text-destructive hover:text-destructive"
													>
														<Trash class="h-4 w-4" />
													</Button>
												{/if}
											</Table.Cell>
										</Table.Row>
									{/each}
								</Table.Body>
							</Table.Root>
						{/if}
					</CardContent>
				</Card>
			{:else if !loading}
				<Card>
					<CardContent class="text-center py-12">
						<Building class="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
						<h3 class="text-lg font-medium mb-2">No Organization Selected</h3>
						<p class="text-muted-foreground mb-4">
							Select an organization from the list to view its details and members.
						</p>
						{#if organizations.length === 0}
							<Button on:click={() => createDialogOpen = true}>
								<Plus class="h-4 w-4 mr-2" />
								Create Organization
							</Button>
						{/if}
					</CardContent>
				</Card>
			{/if}
		</div>
	</div>
</div>

<!-- Create Organization Dialog -->
<Dialog.Root bind:open={createDialogOpen}>
	<Dialog.Content class="sm:max-w-[425px]">
		<Dialog.Header>
			<Dialog.Title>Create Organization</Dialog.Title>
			<Dialog.Description>
				Create a new organization to collaborate with your team.
			</Dialog.Description>
		</Dialog.Header>
		<div class="space-y-4 py-4">
			<div class="space-y-2">
				<Label for="name">Organization Name</Label>
				<Input
					id="name"
					bind:value={createForm.name}
					placeholder="Enter organization name"
				/>
			</div>
			<div class="space-y-2">
				<Label for="slug">Slug</Label>
				<Input
					id="slug"
					bind:value={createForm.slug}
					placeholder="organization-slug"
				/>
				<p class="text-xs text-muted-foreground">
					This will be used in URLs and must be unique.
				</p>
			</div>
			<div class="space-y-2">
				<Label for="description">Description (Optional)</Label>
				<Textarea
					id="description"
					bind:value={createForm.description}
					placeholder="What is this organization for?"
					rows={3}
				/>
			</div>
		</div>
		<Dialog.Footer>
			<Button variant="outline" on:click={() => createDialogOpen = false}>
				Cancel
			</Button>
			<Button 
				on:click={createOrganization}
				disabled={!createForm.name || !createForm.slug}
			>
				Create Organization
			</Button>
		</Dialog.Footer>
	</Dialog.Content>
</Dialog.Root>

<!-- Invite User Dialog -->
<Dialog.Root bind:open={inviteDialogOpen}>
	<Dialog.Content class="sm:max-w-[425px]">
		<Dialog.Header>
			<Dialog.Title>Invite User</Dialog.Title>
			<Dialog.Description>
				Invite a user to join {selectedOrg?.name}.
			</Dialog.Description>
		</Dialog.Header>
		<div class="space-y-4 py-4">
			<div class="space-y-2">
				<Label for="email">Email Address</Label>
				<Input
					id="email"
					type="email"
					bind:value={inviteForm.email}
					placeholder="<EMAIL>"
				/>
			</div>
			<div class="space-y-2">
				<Label for="role">Role</Label>
				<select 
					id="role" 
					bind:value={inviteForm.role}
					class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background"
				>
					<option value="member">Member</option>
					<option value="admin">Admin</option>
				</select>
			</div>
		</div>
		<Dialog.Footer>
			<Button variant="outline" on:click={() => inviteDialogOpen = false}>
				Cancel
			</Button>
			<Button 
				on:click={inviteUser}
				disabled={!inviteForm.email}
			>
				Send Invitation
			</Button>
		</Dialog.Footer>
	</Dialog.Content>
</Dialog.Root>