<script lang="ts">
	import { But<PERSON> } from '$lib/components/ui/button/index.js';
	import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '$lib/components/ui/card/index.js';
	import { Badge } from '$lib/components/ui/badge/index.js';
	import { Input } from '$lib/components/ui/input/index.js';
	import { Label } from '$lib/components/ui/label/index.js';
	import { Avatar, AvatarFallback, AvatarImage } from '$lib/components/ui/avatar/index.js';
	import { Skeleton } from '$lib/components/ui/skeleton/index.js';
	import { Alert, AlertDescription } from '$lib/components/ui/alert/index.js';
	import * as Tabs from '$lib/components/ui/tabs/index.js';
	import { Separator } from '$lib/components/ui/separator/index.js';
	import { onMount } from 'svelte';
	import type { PageData } from './$types';
	import { 
		User, 
		Mail,
		Shield,
		Clock,
		Calendar,
		Save,
		Eye,
		EyeOff,
		Key,
		Bell,
		Globe,
		Trash2,
		AlertTriangle,
		CheckCircle2
	} from 'lucide-svelte';

	export let data: PageData;

	let userProfile: any = null;
	let loading = true;
	let saving = false;
	let error: string | null = null;
	let success: string | null = null;

	// Form states
	let profileForm = {
		name: '',
		image: ''
	};

	let securityForm = {
		currentPassword: '',
		newPassword: '',
		confirmPassword: ''
	};

	let notificationSettings = {
		emailNotifications: true,
		securityAlerts: true,
		marketingEmails: false,
		weeklyReports: true
	};

	let showCurrentPassword = false;
	let showNewPassword = false;
	let showConfirmPassword = false;

	onMount(async () => {
		if (data.user) {
			await loadProfile();
		}
		loading = false;
	});

	async function loadProfile() {
		try {
			userProfile = await data.trpc.users.me.query();
			profileForm = {
				name: userProfile.name || '',
				image: userProfile.image || ''
			};
		} catch (err) {
			console.error('Failed to load profile:', err);
			error = err instanceof Error ? err.message : 'Failed to load profile';
		}
	}

	async function updateProfile() {
		saving = true;
		error = null;
		success = null;

		try {
			await data.trpc.users.updateProfile.mutate({
				name: profileForm.name || undefined,
				image: profileForm.image || null
			});
			
			success = 'Profile updated successfully';
			await loadProfile();
		} catch (err) {
			error = err instanceof Error ? err.message : 'Failed to update profile';
		}
		
		saving = false;
	}

	async function updateLastLogin() {
		try {
			await data.trpc.users.updateLastLogin.mutate();
			await loadProfile();
		} catch (err) {
			console.error('Failed to update last login:', err);
		}
	}

	function getUserInitials(name: string | null | undefined): string {
		if (!name) return 'U';
		return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
	}

	function formatDate(date: Date | string | null | undefined): string {
		if (!date) return 'Never';
		return new Date(date).toLocaleDateString('en-US', {
			year: 'numeric',
			month: 'long',
			day: 'numeric',
			hour: '2-digit',
			minute: '2-digit'
		});
	}

	function clearMessages() {
		error = null;
		success = null;
	}

	// Password validation
	$: passwordsMatch = securityForm.newPassword === securityForm.confirmPassword;
	$: passwordStrong = securityForm.newPassword.length >= 8;
	$: canUpdatePassword = securityForm.currentPassword && 
						   securityForm.newPassword && 
						   passwordsMatch && 
						   passwordStrong;
</script>

<div class="space-y-6">
	<!-- Header -->
	<div>
		<h1 class="text-3xl font-bold tracking-tight">Account Settings</h1>
		<p class="text-muted-foreground">
			Manage your account preferences and security settings
		</p>
	</div>

	{#if error}
		<Alert variant="destructive">
			<AlertTriangle class="h-4 w-4" />
			<AlertDescription>{error}</AlertDescription>
		</Alert>
	{/if}

	{#if success}
		<Alert class="border-green-200 bg-green-50 text-green-900 dark:border-green-800 dark:bg-green-950 dark:text-green-100">
			<CheckCircle2 class="h-4 w-4" />
			<AlertDescription>{success}</AlertDescription>
		</Alert>
	{/if}

	<Tabs.Root value="profile" class="space-y-6">
		<Tabs.List class="grid w-full grid-cols-4">
			<Tabs.Trigger value="profile">Profile</Tabs.Trigger>
			<Tabs.Trigger value="security">Security</Tabs.Trigger>
			<Tabs.Trigger value="notifications">Notifications</Tabs.Trigger>
			<Tabs.Trigger value="danger">Danger Zone</Tabs.Trigger>
		</Tabs.List>

		<!-- Profile Tab -->
		<Tabs.Content value="profile" class="space-y-6">
			<Card>
				<CardHeader>
					<CardTitle class="flex items-center gap-2">
						<User class="h-5 w-5" />
						Profile Information
					</CardTitle>
					<CardDescription>
						Update your personal information and profile picture
					</CardDescription>
				</CardHeader>
				<CardContent class="space-y-6">
					{#if loading}
						<div class="space-y-4">
							<div class="flex items-center gap-4">
								<Skeleton class="h-16 w-16 rounded-full" />
								<div class="space-y-2">
									<Skeleton class="h-4 w-32" />
									<Skeleton class="h-3 w-24" />
								</div>
							</div>
							<Skeleton class="h-10 w-full" />
							<Skeleton class="h-10 w-full" />
						</div>
					{:else if userProfile}
						<!-- Profile Picture -->
						<div class="flex items-center gap-4">
							<Avatar class="h-16 w-16">
								<AvatarImage src={userProfile.image || ''} alt={userProfile.name || 'User'} />
								<AvatarFallback class="text-lg">
									{getUserInitials(userProfile.name)}
								</AvatarFallback>
							</Avatar>
							<div>
								<h3 class="font-medium">{userProfile.name || 'Unnamed User'}</h3>
								<p class="text-sm text-muted-foreground">{userProfile.email}</p>
								<div class="flex items-center gap-2 mt-1">
									<Badge variant={userProfile.role === 'admin' ? 'default' : 'secondary'}>
										{userProfile.role}
									</Badge>
									<Badge variant={userProfile.isActive ? 'default' : 'destructive'}>
										{userProfile.isActive ? 'Active' : 'Inactive'}
									</Badge>
								</div>
							</div>
						</div>

						<Separator />

						<!-- Profile Form -->
						<form on:submit|preventDefault={updateProfile} class="space-y-4">
							<div class="grid gap-4 md:grid-cols-2">
								<div class="space-y-2">
									<Label for="name">Full Name</Label>
									<Input
										id="name"
										bind:value={profileForm.name}
										placeholder="Enter your full name"
										on:input={clearMessages}
									/>
								</div>
								<div class="space-y-2">
									<Label for="email">Email Address</Label>
									<Input
										id="email"
										value={userProfile.email}
										disabled
										class="bg-muted"
									/>
									<p class="text-xs text-muted-foreground">
										Email cannot be changed. Contact support if needed.
									</p>
								</div>
							</div>

							<div class="space-y-2">
								<Label for="image">Profile Image URL</Label>
								<Input
									id="image"
									bind:value={profileForm.image}
									placeholder="https://example.com/avatar.jpg"
									on:input={clearMessages}
								/>
								<p class="text-xs text-muted-foreground">
									Provide a URL to your profile image
								</p>
							</div>

							<Button type="submit" disabled={saving} class="w-full md:w-auto">
								{#if saving}
									<div class="animate-spin h-4 w-4 mr-2 border-2 border-current border-t-transparent rounded-full"></div>
								{:else}
									<Save class="h-4 w-4 mr-2" />
								{/if}
								Save Changes
							</Button>
						</form>

						<Separator />

						<!-- Account Details -->
						<div class="space-y-4">
							<h3 class="font-medium">Account Details</h3>
							<div class="grid gap-4 md:grid-cols-2">
								<div class="space-y-1">
									<Label class="text-sm font-medium text-muted-foreground">Member Since</Label>
									<p class="text-sm">{formatDate(userProfile.createdAt)}</p>
								</div>
								<div class="space-y-1">
									<Label class="text-sm font-medium text-muted-foreground">Last Updated</Label>
									<p class="text-sm">{formatDate(userProfile.updatedAt)}</p>
								</div>
								<div class="space-y-1">
									<Label class="text-sm font-medium text-muted-foreground">Last Login</Label>
									<p class="text-sm">{formatDate(userProfile.lastLoginAt)}</p>
								</div>
								<div class="space-y-1">
									<Label class="text-sm font-medium text-muted-foreground">Email Status</Label>
									<div class="flex items-center gap-2">
										<Badge variant={userProfile.emailVerified ? 'default' : 'destructive'}>
											{userProfile.emailVerified ? 'Verified' : 'Unverified'}
										</Badge>
										{#if !userProfile.emailVerified}
											<Button variant="link" size="sm" class="h-auto p-0">
												Resend verification
											</Button>
										{/if}
									</div>
								</div>
							</div>
						</div>
					{/if}
				</CardContent>
			</Card>
		</Tabs.Content>

		<!-- Security Tab -->
		<Tabs.Content value="security" class="space-y-6">
			<Card>
				<CardHeader>
					<CardTitle class="flex items-center gap-2">
						<Shield class="h-5 w-5" />
						Password & Security
					</CardTitle>
					<CardDescription>
						Keep your account secure with a strong password
					</CardDescription>
				</CardHeader>
				<CardContent class="space-y-6">
					<form class="space-y-4">
						<div class="space-y-2">
							<Label for="current-password">Current Password</Label>
							<div class="relative">
								<Input
									id="current-password"
									type={showCurrentPassword ? 'text' : 'password'}
									bind:value={securityForm.currentPassword}
									placeholder="Enter your current password"
								/>
								<Button
									type="button"
									variant="ghost"
									size="sm"
									class="absolute right-2 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0"
									on:click={() => showCurrentPassword = !showCurrentPassword}
								>
									{#if showCurrentPassword}
										<EyeOff class="h-4 w-4" />
									{:else}
										<Eye class="h-4 w-4" />
									{/if}
								</Button>
							</div>
						</div>

						<div class="space-y-2">
							<Label for="new-password">New Password</Label>
							<div class="relative">
								<Input
									id="new-password"
									type={showNewPassword ? 'text' : 'password'}
									bind:value={securityForm.newPassword}
									placeholder="Enter your new password"
								/>
								<Button
									type="button"
									variant="ghost"
									size="sm"
									class="absolute right-2 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0"
									on:click={() => showNewPassword = !showNewPassword}
								>
									{#if showNewPassword}
										<EyeOff class="h-4 w-4" />
									{:else}
										<Eye class="h-4 w-4" />
									{/if}
								</Button>
							</div>
							{#if securityForm.newPassword && !passwordStrong}
								<p class="text-xs text-destructive">Password must be at least 8 characters long</p>
							{/if}
						</div>

						<div class="space-y-2">
							<Label for="confirm-password">Confirm New Password</Label>
							<div class="relative">
								<Input
									id="confirm-password"
									type={showConfirmPassword ? 'text' : 'password'}
									bind:value={securityForm.confirmPassword}
									placeholder="Confirm your new password"
								/>
								<Button
									type="button"
									variant="ghost"
									size="sm"
									class="absolute right-2 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0"
									on:click={() => showConfirmPassword = !showConfirmPassword}
								>
									{#if showConfirmPassword}
										<EyeOff class="h-4 w-4" />
									{:else}
										<Eye class="h-4 w-4" />
									{/if}
								</Button>
							</div>
							{#if securityForm.confirmPassword && !passwordsMatch}
								<p class="text-xs text-destructive">Passwords do not match</p>
							{/if}
						</div>

						<Button disabled={!canUpdatePassword} class="w-full md:w-auto">
							<Key class="h-4 w-4 mr-2" />
							Update Password
						</Button>
					</form>

					<Separator />

					<div class="space-y-4">
						<h3 class="font-medium">Security Settings</h3>
						<div class="space-y-3">
							<div class="flex items-center justify-between">
								<div>
									<p class="font-medium">Two-Factor Authentication</p>
									<p class="text-sm text-muted-foreground">Add an extra layer of security</p>
								</div>
								<Button variant="outline" disabled>
									Enable 2FA
								</Button>
							</div>
							<div class="flex items-center justify-between">
								<div>
									<p class="font-medium">Login Sessions</p>
									<p class="text-sm text-muted-foreground">Manage your active sessions</p>
								</div>
								<Button variant="outline" disabled>
									View Sessions
								</Button>
							</div>
						</div>
					</div>
				</CardContent>
			</Card>
		</Tabs.Content>

		<!-- Notifications Tab -->
		<Tabs.Content value="notifications" class="space-y-6">
			<Card>
				<CardHeader>
					<CardTitle class="flex items-center gap-2">
						<Bell class="h-5 w-5" />
						Notification Preferences
					</CardTitle>
					<CardDescription>
						Choose how you want to be notified about important updates
					</CardDescription>
				</CardHeader>
				<CardContent class="space-y-6">
					<div class="space-y-4">
						<div class="flex items-center justify-between">
							<div>
								<p class="font-medium">Email Notifications</p>
								<p class="text-sm text-muted-foreground">Receive notifications via email</p>
							</div>
							<Button variant="outline" disabled>
								Configure
							</Button>
						</div>
						<div class="flex items-center justify-between">
							<div>
								<p class="font-medium">Security Alerts</p>
								<p class="text-sm text-muted-foreground">Get notified about security events</p>
							</div>
							<Button variant="outline" disabled>
								Configure
							</Button>
						</div>
						<div class="flex items-center justify-between">
							<div>
								<p class="font-medium">Marketing Emails</p>
								<p class="text-sm text-muted-foreground">Product updates and newsletters</p>
							</div>
							<Button variant="outline" disabled>
								Configure
							</Button>
						</div>
					</div>
				</CardContent>
			</Card>
		</Tabs.Content>

		<!-- Danger Zone Tab -->
		<Tabs.Content value="danger" class="space-y-6">
			<Card class="border-destructive">
				<CardHeader>
					<CardTitle class="flex items-center gap-2 text-destructive">
						<AlertTriangle class="h-5 w-5" />
						Danger Zone
					</CardTitle>
					<CardDescription>
						Irreversible and destructive actions
					</CardDescription>
				</CardHeader>
				<CardContent class="space-y-6">
					<div class="space-y-4">
						<div class="flex items-center justify-between p-4 border border-destructive rounded-lg">
							<div>
								<p class="font-medium">Delete Account</p>
								<p class="text-sm text-muted-foreground">
									Permanently delete your account and all associated data
								</p>
							</div>
							<Button variant="destructive" disabled>
								<Trash2 class="h-4 w-4 mr-2" />
								Delete Account
							</Button>
						</div>
						<div class="flex items-center justify-between p-4 border border-destructive rounded-lg">
							<div>
								<p class="font-medium">Export Data</p>
								<p class="text-sm text-muted-foreground">
									Download a copy of all your data
								</p>
							</div>
							<Button variant="outline" disabled>
								Export Data
							</Button>
						</div>
					</div>
				</CardContent>
			</Card>
		</Tabs.Content>
	</Tabs.Root>
</div>