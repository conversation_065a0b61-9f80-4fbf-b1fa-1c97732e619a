import type { Request<PERSON><PERSON><PERSON> } from './$types';
import { fetchRequestHandler } from '@trpc/server/adapters/fetch';
import { appRouter } from '$lib/server/trpc/root';
import { createContext } from '$lib/server/trpc/context';

const handler: RequestHandler = async (event) => {
	return fetchRequestHandler({
		endpoint: '/api/trpc',
		req: event.request,
		router: appRouter,
		createContext: () => createContext(event),
		onError: ({ error, path }) => {
			console.error(`❌ tRPC failed on ${path}:`, error);
		}
	});
};

export const GET = handler;
export const POST = handler;