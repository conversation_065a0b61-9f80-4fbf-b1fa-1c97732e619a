import { serve } from 'inngest/sveltekit';
import { inngest } from '$lib/server/inngest';
import { inngestFunctions } from '$lib/server/inngest/functions';
import { features } from '$lib/server/env';
import type { RequestHandler } from './$types';

// Fallback handlers when Inngest is not configured
const fallbackHandler: RequestHandler = () => {
	return new Response('Inngest not configured', { status: 503 });
};

let handlers: { GET: RequestHandler; POST: RequestHandler; PUT: RequestHandler };

// Only serve Inngest if it's configured
if (features.inngest) {
	try {
		handlers = serve({
			client: inngest,
			functions: inngestFunctions,
			serve: 'inngest'
		});
	} catch (error) {
		console.error('Failed to initialize Inngest handlers:', error);
		handlers = {
			GET: fallbackHandler,
			POST: fallbackHandler,
			PUT: fallbackHandler
		};
	}
} else {
	handlers = {
		GET: fallbackHandler,
		POST: fallbackHandler,
		PUT: fallbackHandler
	};
}

export const GET = handlers.GET;
export const POST = handlers.POST;
export const PUT = handlers.PUT;