<script lang="ts">
	import { But<PERSON> } from '$lib/components/ui/button/index.js';
	import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '$lib/components/ui/card/index.js';
	import { Badge } from '$lib/components/ui/badge/index.js';
	import { Input } from '$lib/components/ui/input/index.js';
	import { Avatar, AvatarFallback, AvatarImage } from '$lib/components/ui/avatar/index.js';
	import { Skeleton } from '$lib/components/ui/skeleton/index.js';
	import { Alert, AlertDescription } from '$lib/components/ui/alert/index.js';
	import * as Table from '$lib/components/ui/table/index.js';
	import * as Dialog from '$lib/components/ui/dialog/index.js';
	import * as Select from '$lib/components/ui/select/index.js';
	import * as Tabs from '$lib/components/ui/tabs/index.js';
	import { onMount } from 'svelte';
	import type { PageData } from './$types';
	import { 
		Users,
		Building,
		Search,
		UserCheck,
		UserX,
		Crown,
		Shield,
		User,
		Activity,
		TrendingUp,
		AlertTriangle,
		MoreHorizontal,
		Settings,
		Eye,
		Edit,
		Trash,
		Plus
	} from 'lucide-svelte';

	export let data: PageData;

	let users: any[] = [];
	let userStats: any = null;
	let loading = true;
	let usersLoading = false;
	let error: string | null = null;
	let searchQuery = '';
	let selectedRole = '';
	let selectedStatus = '';
	let userDialogOpen = false;
	let selectedUser: any = null;

	// Pagination
	let currentPage = 1;
	let totalPages = 1;
	let pageSize = 20;

	onMount(async () => {
		if (data.user?.role !== 'admin') {
			error = 'Access denied. Admin privileges required.';
			loading = false;
			return;
		}
		
		await loadUsers();
		await loadStats();
		loading = false;
	});

	async function loadUsers() {
		usersLoading = true;
		try {
			const result = await data.trpc.users.list.query({
				page: currentPage,
				limit: pageSize,
				search: searchQuery || undefined,
				role: selectedRole as 'user' | 'admin' || undefined,
				isActive: selectedStatus === 'active' ? true : selectedStatus === 'inactive' ? false : undefined
			});
			
			users = result.users;
			totalPages = result.pagination.totalPages;
		} catch (err) {
			console.error('Failed to load users:', err);
			error = err instanceof Error ? err.message : 'Failed to load users';
		}
		usersLoading = false;
	}

	async function loadStats() {
		try {
			userStats = await data.trpc.users.stats.query();
		} catch (err) {
			console.error('Failed to load stats:', err);
		}
	}

	async function updateUserRole(userId: string, newRole: 'user' | 'admin') {
		try {
			await data.trpc.users.updateRole.mutate({
				userId,
				role: newRole
			});
			await loadUsers();
		} catch (err) {
			error = err instanceof Error ? err.message : 'Failed to update user role';
		}
	}

	async function deactivateUser(userId: string) {
		try {
			await data.trpc.users.deactivate.mutate({ userId });
			await loadUsers();
		} catch (err) {
			error = err instanceof Error ? err.message : 'Failed to deactivate user';
		}
	}

	function getUserInitials(name: string | null | undefined): string {
		if (!name) return 'U';
		return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
	}

	function formatDate(date: Date | string): string {
		return new Date(date).toLocaleDateString('en-US', {
			year: 'numeric',
			month: 'short',
			day: 'numeric'
		});
	}

	function getRoleIcon(role: string) {
		switch (role) {
			case 'admin': return Crown;
			case 'user': return User;
			default: return User;
		}
	}

	function getRoleBadgeVariant(role: string) {
		switch (role) {
			case 'admin': return 'default';
			case 'user': return 'secondary';
			default: return 'outline';
		}
	}

	// Reactive statements for filtering
	$: {
		if (searchQuery !== undefined || selectedRole !== undefined || selectedStatus !== undefined) {
			currentPage = 1;
			loadUsers();
		}
	}

	function nextPage() {
		if (currentPage < totalPages) {
			currentPage++;
			loadUsers();
		}
	}

	function prevPage() {
		if (currentPage > 1) {
			currentPage--;
			loadUsers();
		}
	}

	function openUserDialog(user: any) {
		selectedUser = user;
		userDialogOpen = true;
	}
</script>

<div class="space-y-6">
	<!-- Header -->
	<div>
		<h1 class="text-3xl font-bold tracking-tight">Admin Dashboard</h1>
		<p class="text-muted-foreground">
			Manage users, organizations, and system settings
		</p>
	</div>

	{#if error}
		<Alert variant="destructive">
			<AlertTriangle class="h-4 w-4" />
			<AlertDescription>{error}</AlertDescription>
		</Alert>
	{/if}

	{#if data.user?.role !== 'admin'}
		<Card>
			<CardContent class="text-center py-12">
				<AlertTriangle class="h-12 w-12 mx-auto mb-4 text-destructive" />
				<h3 class="text-lg font-medium mb-2">Access Denied</h3>
				<p class="text-muted-foreground">
					You need administrator privileges to access this page.
				</p>
			</CardContent>
		</Card>
	{:else}
		<!-- Stats Overview -->
		<div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
			{#if loading}
				{#each Array(4) as _}
					<Card>
						<CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
							<Skeleton class="h-4 w-24" />
							<Skeleton class="h-4 w-4" />
						</CardHeader>
						<CardContent>
							<Skeleton class="h-7 w-16 mb-1" />
							<Skeleton class="h-3 w-32" />
						</CardContent>
					</Card>
				{/each}
			{:else if userStats}
				<Card>
					<CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle class="text-sm font-medium">Total Users</CardTitle>
						<Users class="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div class="text-2xl font-bold">{userStats.totalUsers}</div>
						<p class="text-xs text-muted-foreground">
							Active users in the system
						</p>
					</CardContent>
				</Card>

				<Card>
					<CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle class="text-sm font-medium">Administrators</CardTitle>
						<Crown class="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div class="text-2xl font-bold">{userStats.totalAdmins}</div>
						<p class="text-xs text-muted-foreground">
							Users with admin privileges
						</p>
					</CardContent>
				</Card>

				<Card>
					<CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle class="text-sm font-medium">Organizations</CardTitle>
						<Building class="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div class="text-2xl font-bold">0</div>
						<p class="text-xs text-muted-foreground">
							Active organizations
						</p>
					</CardContent>
				</Card>

				<Card>
					<CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle class="text-sm font-medium">System Health</CardTitle>
						<Activity class="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div class="text-2xl font-bold text-green-600">Healthy</div>
						<p class="text-xs text-muted-foreground">
							All systems operational
						</p>
					</CardContent>
				</Card>
			{/if}
		</div>

		<Tabs.Root value="users" class="space-y-6">
			<Tabs.List>
				<Tabs.Trigger value="users">User Management</Tabs.Trigger>
				<Tabs.Trigger value="organizations">Organizations</Tabs.Trigger>
				<Tabs.Trigger value="system">System Settings</Tabs.Trigger>
			</Tabs.List>

			<!-- Users Tab -->
			<Tabs.Content value="users" class="space-y-6">
				<Card>
					<CardHeader>
						<div class="flex items-center justify-between">
							<div>
								<CardTitle class="flex items-center gap-2">
									<Users class="h-5 w-5" />
									User Management
								</CardTitle>
								<CardDescription>
									View and manage all users in the system
								</CardDescription>
							</div>
							<Button disabled>
								<Plus class="h-4 w-4 mr-2" />
								Add User
							</Button>
						</div>
					</CardHeader>
					<CardContent class="space-y-4">
						<!-- Filters -->
						<div class="flex gap-4 items-end">
							<div class="flex-1">
								<div class="relative">
									<Search class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
									<Input
										placeholder="Search users..."
										bind:value={searchQuery}
										class="pl-10"
									/>
								</div>
							</div>
							<Select.Root 
								onSelectedChange={(value) => selectedRole = value?.value || ''}
								selected={{ value: selectedRole, label: selectedRole || 'All Roles' }}
							>
								<Select.Trigger class="w-40">
									<Select.Value placeholder="All Roles" />
								</Select.Trigger>
								<Select.Content>
									<Select.Item value="">All Roles</Select.Item>
									<Select.Item value="admin">Admin</Select.Item>
									<Select.Item value="user">User</Select.Item>
								</Select.Content>
							</Select.Root>
							<Select.Root 
								onSelectedChange={(value) => selectedStatus = value?.value || ''}
								selected={{ value: selectedStatus, label: selectedStatus || 'All Status' }}
							>
								<Select.Trigger class="w-40">
									<Select.Value placeholder="All Status" />
								</Select.Trigger>
								<Select.Content>
									<Select.Item value="">All Status</Select.Item>
									<Select.Item value="active">Active</Select.Item>
									<Select.Item value="inactive">Inactive</Select.Item>
								</Select.Content>
							</Select.Root>
						</div>

						<!-- Users Table -->
						{#if usersLoading}
							<div class="space-y-2">
								{#each Array(5) as _}
									<div class="flex items-center gap-3 p-2">
										<Skeleton class="h-8 w-8 rounded-full" />
										<div class="space-y-1 flex-1">
											<Skeleton class="h-4 w-32" />
											<Skeleton class="h-3 w-24" />
										</div>
										<Skeleton class="h-6 w-16" />
									</div>
								{/each}
							</div>
						{:else if users.length === 0}
							<div class="text-center py-8 text-muted-foreground">
								<Users class="h-8 w-8 mx-auto mb-2 opacity-50" />
								<p class="text-sm">No users found</p>
							</div>
						{:else}
							<Table.Root>
								<Table.Header>
									<Table.Row>
										<Table.Head>User</Table.Head>
										<Table.Head>Role</Table.Head>
										<Table.Head>Status</Table.Head>
										<Table.Head>Last Login</Table.Head>
										<Table.Head>Joined</Table.Head>
										<Table.Head class="w-12"></Table.Head>
									</Table.Row>
								</Table.Header>
								<Table.Body>
									{#each users as user}
										<Table.Row>
											<Table.Cell>
												<div class="flex items-center gap-3">
													<Avatar class="h-8 w-8">
														<AvatarImage src={user.image || ''} alt={user.name || 'User'} />
														<AvatarFallback>{getUserInitials(user.name)}</AvatarFallback>
													</Avatar>
													<div>
														<p class="font-medium">{user.name || 'Unnamed User'}</p>
														<p class="text-sm text-muted-foreground">{user.email}</p>
													</div>
												</div>
											</Table.Cell>
											<Table.Cell>
												<Badge variant={getRoleBadgeVariant(user.role)} class="flex items-center gap-1 w-fit">
													<svelte:component this={getRoleIcon(user.role)} class="h-3 w-3" />
													{user.role}
												</Badge>
											</Table.Cell>
											<Table.Cell>
												<Badge variant={user.isActive ? 'default' : 'destructive'}>
													{user.isActive ? 'Active' : 'Inactive'}
												</Badge>
											</Table.Cell>
											<Table.Cell class="text-muted-foreground">
												{user.lastLoginAt ? formatDate(user.lastLoginAt) : 'Never'}
											</Table.Cell>
											<Table.Cell class="text-muted-foreground">
												{formatDate(user.createdAt)}
											</Table.Cell>
											<Table.Cell>
												<Button
													variant="ghost"
													size="sm"
													on:click={() => openUserDialog(user)}
												>
													<MoreHorizontal class="h-4 w-4" />
												</Button>
											</Table.Cell>
										</Table.Row>
									{/each}
								</Table.Body>
							</Table.Root>

							<!-- Pagination -->
							<div class="flex items-center justify-between">
								<p class="text-sm text-muted-foreground">
									Page {currentPage} of {totalPages}
								</p>
								<div class="flex gap-2">
									<Button variant="outline" size="sm" on:click={prevPage} disabled={currentPage === 1}>
										Previous
									</Button>
									<Button variant="outline" size="sm" on:click={nextPage} disabled={currentPage === totalPages}>
										Next
									</Button>
								</div>
							</div>
						{/if}
					</CardContent>
				</Card>
			</Tabs.Content>

			<!-- Organizations Tab -->
			<Tabs.Content value="organizations" class="space-y-6">
				<Card>
					<CardHeader>
						<CardTitle class="flex items-center gap-2">
							<Building class="h-5 w-5" />
							Organization Management
						</CardTitle>
						<CardDescription>
							View and manage all organizations
						</CardDescription>
					</CardHeader>
					<CardContent>
						<div class="text-center py-12 text-muted-foreground">
							<Building class="h-12 w-12 mx-auto mb-4 opacity-50" />
							<h3 class="text-lg font-medium mb-2">Coming Soon</h3>
							<p class="text-sm">
								Organization management features will be available in the next update.
							</p>
						</div>
					</CardContent>
				</Card>
			</Tabs.Content>

			<!-- System Tab -->
			<Tabs.Content value="system" class="space-y-6">
				<Card>
					<CardHeader>
						<CardTitle class="flex items-center gap-2">
							<Settings class="h-5 w-5" />
							System Settings
						</CardTitle>
						<CardDescription>
							Configure system-wide settings and preferences
						</CardDescription>
					</CardHeader>
					<CardContent>
						<div class="text-center py-12 text-muted-foreground">
							<Settings class="h-12 w-12 mx-auto mb-4 opacity-50" />
							<h3 class="text-lg font-medium mb-2">Coming Soon</h3>
							<p class="text-sm">
								System settings will be available in the next update.
							</p>
						</div>
					</CardContent>
				</Card>
			</Tabs.Content>
		</Tabs.Root>
	{/if}
</div>

<!-- User Details Dialog -->
<Dialog.Root bind:open={userDialogOpen}>
	<Dialog.Content class="sm:max-w-[425px]">
		{#if selectedUser}
			<Dialog.Header>
				<Dialog.Title>User Details</Dialog.Title>
				<Dialog.Description>
					Manage {selectedUser.name || selectedUser.email}
				</Dialog.Description>
			</Dialog.Header>
			<div class="space-y-4 py-4">
				<div class="flex items-center gap-3">
					<Avatar class="h-12 w-12">
						<AvatarImage src={selectedUser.image || ''} alt={selectedUser.name || 'User'} />
						<AvatarFallback>{getUserInitials(selectedUser.name)}</AvatarFallback>
					</Avatar>
					<div>
						<p class="font-medium">{selectedUser.name || 'Unnamed User'}</p>
						<p class="text-sm text-muted-foreground">{selectedUser.email}</p>
					</div>
				</div>
				
				<div class="space-y-2">
					<div class="flex justify-between">
						<span class="text-sm font-medium">Role:</span>
						<Badge variant={getRoleBadgeVariant(selectedUser.role)}>
							{selectedUser.role}
						</Badge>
					</div>
					<div class="flex justify-between">
						<span class="text-sm font-medium">Status:</span>
						<Badge variant={selectedUser.isActive ? 'default' : 'destructive'}>
							{selectedUser.isActive ? 'Active' : 'Inactive'}
						</Badge>
					</div>
					<div class="flex justify-between">
						<span class="text-sm font-medium">Joined:</span>
						<span class="text-sm">{formatDate(selectedUser.createdAt)}</span>
					</div>
					<div class="flex justify-between">
						<span class="text-sm font-medium">Last Login:</span>
						<span class="text-sm">{selectedUser.lastLoginAt ? formatDate(selectedUser.lastLoginAt) : 'Never'}</span>
					</div>
				</div>
			</div>
			<Dialog.Footer class="flex gap-2">
				{#if selectedUser.id !== data.user?.id}
					{#if selectedUser.role === 'user'}
						<Button 
							variant="outline" 
							on:click={() => updateUserRole(selectedUser.id, 'admin')}
						>
							Make Admin
						</Button>
					{:else}
						<Button 
							variant="outline" 
							on:click={() => updateUserRole(selectedUser.id, 'user')}
						>
							Remove Admin
						</Button>
					{/if}
					{#if selectedUser.isActive}
						<Button 
							variant="destructive" 
							on:click={() => deactivateUser(selectedUser.id)}
						>
							Deactivate
						</Button>
					{/if}
				{/if}
				<Button variant="outline" on:click={() => userDialogOpen = false}>
					Close
				</Button>
			</Dialog.Footer>
		{/if}
	</Dialog.Content>
</Dialog.Root>