import { browser } from '$app/environment';
import { redirect } from '@sveltejs/kit';
import type { LayoutLoad } from './$types';

export const load: LayoutLoad = async ({ parent }) => {
	const data = await parent();
	
	// Redirect to login if not authenticated
	if (!data.user) {
		if (browser) {
			redirect(302, '/auth/signin');
		}
	}
	
	// Redirect to dashboard if not admin
	if (data.user && data.user.role !== 'admin') {
		if (browser) {
			redirect(302, '/');
		}
	}
	
	return data;
};