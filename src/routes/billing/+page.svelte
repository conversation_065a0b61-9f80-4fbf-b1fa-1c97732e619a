<script lang="ts">
	import { But<PERSON> } from '$lib/components/ui/button/index.js';
	import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '$lib/components/ui/card/index.js';
	import { Badge } from '$lib/components/ui/badge/index.js';
	import { Skeleton } from '$lib/components/ui/skeleton/index.js';
	import { Alert, AlertDescription } from '$lib/components/ui/alert/index.js';
	import { Progress } from '$lib/components/ui/progress/index.js';
	import * as Dialog from '$lib/components/ui/dialog/index.js';
	import * as Table from '$lib/components/ui/table/index.js';
	import * as Select from '$lib/components/ui/select/index.js';
	import { onMount } from 'svelte';
	import type { PageData } from './$types';
	import { 
		CreditCard, 
		Zap,
		Check,
		X,
		Calendar,
		Download,
		TrendingUp,
		AlertTriangle,
		ExternalLink,
		Wallet,
		BarChart3,
		Users,
		Database,
		Activity
	} from 'lucide-svelte';

	export let data: PageData;

	let userOrgs: any[] = [];
	let selectedOrg: any = null;
	let plans: any[] = [];
	let currentSubscription: any = null;
	let usage: any = null;
	let invoices: any[] = [];
	let loading = true;
	let subscriptionLoading = false;
	let usageLoading = false;
	let invoicesLoading = false;
	let error: string | null = null;
	let planChangeDialogOpen = false;
	let cancelDialogOpen = false;

	onMount(async () => {
		if (data.user) {
			await loadData();
		}
		loading = false;
	});

	async function loadData() {
		try {
			const [orgs, plansData] = await Promise.all([
				data.trpc.organizations.mine.query(),
				data.trpc.billing.plans.query()
			]);
			
			userOrgs = orgs;
			plans = plansData;
			
			if (userOrgs.length > 0 && !selectedOrg) {
				selectedOrg = userOrgs[0];
				await loadOrgData();
			}
		} catch (err) {
			console.error('Failed to load billing data:', err);
			error = err instanceof Error ? err.message : 'Failed to load billing data';
		}
	}

	async function loadOrgData() {
		if (!selectedOrg) return;
		
		subscriptionLoading = true;
		usageLoading = true;
		invoicesLoading = true;
		
		try {
			const [sub, usageData, invoiceData] = await Promise.all([
				data.trpc.billing.currentSubscription.query({ organizationId: selectedOrg.id }),
				data.trpc.billing.usage.query({ organizationId: selectedOrg.id }),
				data.trpc.billing.invoices.query({ organizationId: selectedOrg.id, limit: 10 })
			]);
			
			currentSubscription = sub;
			usage = usageData;
			invoices = invoiceData;
		} catch (err) {
			console.error('Failed to load organization billing data:', err);
		}
		
		subscriptionLoading = false;
		usageLoading = false;
		invoicesLoading = false;
	}

	async function createCheckout(priceId: string) {
		if (!selectedOrg) return;
		
		try {
			const checkout = await data.trpc.billing.createCheckout.mutate({
				organizationId: selectedOrg.id,
				priceId,
				successUrl: `${window.location.origin}/billing?success=true`,
				cancelUrl: `${window.location.origin}/billing?canceled=true`
			});
			
			window.location.href = checkout.url;
		} catch (err) {
			error = err instanceof Error ? err.message : 'Failed to create checkout session';
		}
	}

	async function cancelSubscription() {
		if (!currentSubscription) return;
		
		try {
			await data.trpc.billing.cancelSubscription.mutate({
				subscriptionId: currentSubscription.id
			});
			cancelDialogOpen = false;
			await loadOrgData();
		} catch (err) {
			error = err instanceof Error ? err.message : 'Failed to cancel subscription';
		}
	}

	function formatDate(date: Date | string): string {
		return new Date(date).toLocaleDateString('en-US', {
			year: 'numeric',
			month: 'short',
			day: 'numeric'
		});
	}

	function formatPrice(price: number): string {
		return new Intl.NumberFormat('en-US', {
			style: 'currency',
			currency: 'USD'
		}).format(price);
	}

	function getStatusBadge(status: string) {
		switch (status) {
			case 'active':
				return { variant: 'default' as const, label: 'Active' };
			case 'canceled':
				return { variant: 'destructive' as const, label: 'Canceled' };
			case 'past_due':
				return { variant: 'destructive' as const, label: 'Past Due' };
			case 'trialing':
				return { variant: 'secondary' as const, label: 'Trial' };
			case 'paid':
				return { variant: 'default' as const, label: 'Paid' };
			case 'open':
				return { variant: 'secondary' as const, label: 'Pending' };
			default:
				return { variant: 'outline' as const, label: status };
		}
	}

	function getCurrentPlan() {
		if (!currentSubscription) return null;
		return plans.find(plan => plan.id === currentSubscription.plan);
	}

	function canManageBilling() {
		return selectedOrg && (selectedOrg.role === 'owner' || selectedOrg.role === 'admin');
	}
</script>

<div class="space-y-6">
	<!-- Header -->
	<div class="flex items-center justify-between">
		<div>
			<h1 class="text-3xl font-bold tracking-tight">Billing & Subscriptions</h1>
			<p class="text-muted-foreground">
				Manage your subscriptions and billing information
			</p>
		</div>
		{#if !currentSubscription && canManageBilling()}
			<Button href="#plans">
				<Zap class="h-4 w-4 mr-2" />
				Upgrade Plan
			</Button>
		{/if}
	</div>

	{#if error}
		<Alert variant="destructive">
			<AlertTriangle class="h-4 w-4" />
			<AlertDescription>{error}</AlertDescription>
		</Alert>
	{/if}

	<!-- Organization Selector -->
	{#if userOrgs.length > 1}
		<Card>
			<CardHeader>
				<CardTitle>Select Organization</CardTitle>
				<CardDescription>Choose an organization to view billing information</CardDescription>
			</CardHeader>
			<CardContent>
				<Select.Root 
					onSelectedChange={(value) => {
						selectedOrg = userOrgs.find(org => org.id === value.value);
						loadOrgData();
					}}
					selected={{ value: selectedOrg?.id, label: selectedOrg?.name }}
				>
					<Select.Trigger class="w-64">
						<Select.Value placeholder="Select organization" />
					</Select.Trigger>
					<Select.Content>
						{#each userOrgs as org}
							<Select.Item value={org.id}>{org.name}</Select.Item>
						{/each}
					</Select.Content>
				</Select.Root>
			</CardContent>
		</Card>
	{/if}

	{#if selectedOrg}
		<!-- Current Subscription -->
		<div class="grid gap-6 lg:grid-cols-2">
			<Card>
				<CardHeader>
					<CardTitle class="flex items-center gap-2">
						<CreditCard class="h-5 w-5" />
						Current Subscription
					</CardTitle>
					<CardDescription>Subscription details for {selectedOrg.name}</CardDescription>
				</CardHeader>
				<CardContent class="space-y-4">
					{#if subscriptionLoading}
						<div class="space-y-2">
							<Skeleton class="h-4 w-24" />
							<Skeleton class="h-4 w-32" />
							<Skeleton class="h-4 w-20" />
						</div>
					{:else if currentSubscription}
						{@const plan = getCurrentPlan()}
						<div class="space-y-3">
							<div class="flex items-center justify-between">
								<span class="font-medium">{plan?.name || currentSubscription.plan}</span>
								<Badge variant={getStatusBadge(currentSubscription.status).variant}>
									{getStatusBadge(currentSubscription.status).label}
								</Badge>
							</div>
							<div class="text-2xl font-bold">
								{plan ? formatPrice(plan.price) : formatPrice(0)}/month
							</div>
							{#if currentSubscription.currentPeriodEnd}
								<p class="text-sm text-muted-foreground">
									Next billing date: {formatDate(currentSubscription.currentPeriodEnd)}
								</p>
							{/if}
							{#if canManageBilling() && currentSubscription.status === 'active'}
								<div class="flex gap-2 pt-2">
									<Button variant="outline" on:click={() => planChangeDialogOpen = true}>
										Change Plan
									</Button>
									<Button variant="destructive" on:click={() => cancelDialogOpen = true}>
										Cancel
									</Button>
								</div>
							{/if}
						</div>
					{:else}
						<div class="text-center py-6">
							<Wallet class="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
							<p class="text-sm text-muted-foreground mb-4">No active subscription</p>
							{#if canManageBilling()}
								<Button href="#plans">Choose a Plan</Button>
							{/if}
						</div>
					{/if}
				</CardContent>
			</Card>

			<!-- Usage Overview -->
			<Card>
				<CardHeader>
					<CardTitle class="flex items-center gap-2">
						<BarChart3 class="h-5 w-5" />
						Usage Overview
					</CardTitle>
					<CardDescription>Current billing period usage</CardDescription>
				</CardHeader>
				<CardContent class="space-y-4">
					{#if usageLoading}
						<div class="space-y-3">
							{#each Array(3) as _}
								<div class="space-y-2">
									<Skeleton class="h-4 w-24" />
									<Skeleton class="h-2 w-full" />
								</div>
							{/each}
						</div>
					{:else if usage}
						<div class="space-y-4">
							<div class="space-y-2">
								<div class="flex justify-between text-sm">
									<span class="flex items-center gap-2">
										<Activity class="h-4 w-4" />
										API Calls
									</span>
									<span>{usage.apiCalls.current.toLocaleString()} / {usage.apiCalls.limit.toLocaleString()}</span>
								</div>
								<Progress value={usage.apiCalls.percentage} class="h-2" />
							</div>
							<div class="space-y-2">
								<div class="flex justify-between text-sm">
									<span class="flex items-center gap-2">
										<Database class="h-4 w-4" />
										Storage
									</span>
									<span>{usage.storage.current}GB / {usage.storage.limit}GB</span>
								</div>
								<Progress value={usage.storage.percentage} class="h-2" />
							</div>
							<div class="space-y-2">
								<div class="flex justify-between text-sm">
									<span class="flex items-center gap-2">
										<Users class="h-4 w-4" />
										Team Members
									</span>
									<span>{usage.teamMembers.current} / {usage.teamMembers.limit}</span>
								</div>
								<Progress value={usage.teamMembers.percentage} class="h-2" />
							</div>
						</div>
					{:else}
						<div class="text-center py-6 text-muted-foreground">
							<TrendingUp class="h-8 w-8 mx-auto mb-2 opacity-50" />
							<p class="text-sm">No usage data available</p>
						</div>
					{/if}
				</CardContent>
			</Card>
		</div>

		<!-- Billing History -->
		<Card>
			<CardHeader>
				<CardTitle class="flex items-center gap-2">
					<Calendar class="h-5 w-5" />
					Billing History
				</CardTitle>
				<CardDescription>Recent invoices and payments</CardDescription>
			</CardHeader>
			<CardContent>
				{#if invoicesLoading}
					<div class="space-y-2">
						{#each Array(3) as _}
							<div class="flex items-center gap-3 p-2">
								<Skeleton class="h-4 w-20" />
								<Skeleton class="h-4 w-24" />
								<Skeleton class="h-4 w-16" />
								<Skeleton class="h-4 w-12" />
							</div>
						{/each}
					</div>
				{:else if invoices.length > 0}
					<Table.Root>
						<Table.Header>
							<Table.Row>
								<Table.Head>Date</Table.Head>
								<Table.Head>Amount</Table.Head>
								<Table.Head>Status</Table.Head>
								<Table.Head>Period</Table.Head>
								<Table.Head class="w-12"></Table.Head>
							</Table.Row>
						</Table.Header>
						<Table.Body>
							{#each invoices as invoice}
								<Table.Row>
									<Table.Cell>
										{formatDate(invoice.createdAt)}
									</Table.Cell>
									<Table.Cell class="font-medium">
										{formatPrice(Number(invoice.amountPaid || invoice.amountDue || 0))}
									</Table.Cell>
									<Table.Cell>
										<Badge variant={getStatusBadge(invoice.status).variant}>
											{getStatusBadge(invoice.status).label}
										</Badge>
									</Table.Cell>
									<Table.Cell class="text-muted-foreground">
										{#if invoice.periodStart && invoice.periodEnd}
											{formatDate(invoice.periodStart)} - {formatDate(invoice.periodEnd)}
										{:else}
											-
										{/if}
									</Table.Cell>
									<Table.Cell>
										<Button variant="ghost" size="sm">
											<Download class="h-4 w-4" />
										</Button>
									</Table.Cell>
								</Table.Row>
							{/each}
						</Table.Body>
					</Table.Root>
				{:else}
					<div class="text-center py-8 text-muted-foreground">
						<Calendar class="h-8 w-8 mx-auto mb-2 opacity-50" />
						<p class="text-sm">No billing history found</p>
					</div>
				{/if}
			</CardContent>
		</Card>
	{/if}

	<!-- Available Plans -->
	<div id="plans">
		<Card>
			<CardHeader>
				<CardTitle class="flex items-center gap-2">
					<Zap class="h-5 w-5" />
					Available Plans
				</CardTitle>
				<CardDescription>
					{currentSubscription ? 'Upgrade or change your current plan' : 'Choose a plan to get started'}
				</CardDescription>
			</CardHeader>
			<CardContent>
				{#if loading}
					<div class="grid gap-4 md:grid-cols-3">
						{#each Array(3) as _}
							<Card>
								<CardHeader>
									<Skeleton class="h-6 w-24" />
									<Skeleton class="h-4 w-32" />
								</CardHeader>
								<CardContent>
									<Skeleton class="h-8 w-20 mb-4" />
									<div class="space-y-2">
										{#each Array(4) as _}
											<Skeleton class="h-4 w-full" />
										{/each}
									</div>
								</CardContent>
							</Card>
						{/each}
					</div>
				{:else}
					<div class="grid gap-4 md:grid-cols-3">
						{#each plans as plan}
							<Card class="relative {getCurrentPlan()?.id === plan.id ? 'border-primary shadow-lg' : ''}">
								{#if getCurrentPlan()?.id === plan.id}
									<div class="absolute -top-2 left-1/2 transform -translate-x-1/2">
										<Badge class="bg-primary text-primary-foreground">Current Plan</Badge>
									</div>
								{/if}
								<CardHeader>
									<CardTitle>{plan.name}</CardTitle>
									<CardDescription>{plan.description}</CardDescription>
									<div class="text-3xl font-bold">
										{formatPrice(plan.price)}
										<span class="text-sm font-normal text-muted-foreground">/{plan.interval}</span>
									</div>
								</CardHeader>
								<CardContent class="space-y-4">
									<ul class="space-y-2">
										{#each plan.features as feature}
											<li class="flex items-center gap-2 text-sm">
												<Check class="h-4 w-4 text-green-500" />
												{feature}
											</li>
										{/each}
									</ul>
									{#if canManageBilling() && getCurrentPlan()?.id !== plan.id}
										<Button 
											class="w-full" 
											variant={plan.id === 'pro' ? 'default' : 'outline'}
											on:click={() => createCheckout(plan.priceId)}
										>
											{currentSubscription ? 'Switch to ' + plan.name : 'Choose ' + plan.name}
										</Button>
									{:else if !canManageBilling()}
										<Button class="w-full" variant="outline" disabled>
											Contact Admin
										</Button>
									{/if}
								</CardContent>
							</Card>
						{/each}
					</div>
				{/if}
			</CardContent>
		</Card>
	</div>
</div>

<!-- Cancel Subscription Dialog -->
<Dialog.Root bind:open={cancelDialogOpen}>
	<Dialog.Content>
		<Dialog.Header>
			<Dialog.Title>Cancel Subscription</Dialog.Title>
			<Dialog.Description>
				Are you sure you want to cancel your subscription? You'll continue to have access until the end of your current billing period.
			</Dialog.Description>
		</Dialog.Header>
		<Dialog.Footer>
			<Button variant="outline" on:click={() => cancelDialogOpen = false}>
				Keep Subscription
			</Button>
			<Button variant="destructive" on:click={cancelSubscription}>
				Cancel Subscription
			</Button>
		</Dialog.Footer>
	</Dialog.Content>
</Dialog.Root>