import { auth } from "$lib/server/auth";
import { redirect } from "@sveltejs/kit";
import type { RequestEvent } from "@sveltejs/kit";

export async function getSession(event: RequestEvent) {
	return await auth.api.getSession({
		headers: event.request.headers
	});
}

export async function requireAuth(event: RequestEvent) {
	const session = await getSession(event);
	
	if (!session) {
		throw redirect(302, '/auth/signin');
	}
	
	return session;
}

export async function requireRole(event: RequestEvent, requiredRole: string) {
	const session = await requireAuth(event);
	
	// @ts-ignore - Better Auth user type doesn't include role but our schema does
	if (session.user.role !== requiredRole && session.user.role !== 'admin') {
		throw redirect(302, '/unauthorized');
	}
	
	return session;
}

export async function requireAdmin(event: RequestEvent) {
	return await requireRole(event, 'admin');
}