import { initTR<PERSON>, TRPCError } from '@trpc/server';
import { z } from 'zod';
import type { Context } from './context';
import { rateLimiters, rateLimitMiddleware } from '../ratelimit';

const t = initTRPC.context<Context>().create();

// Base router and procedure helpers
export const router = t.router;

// Rate limiting middleware
const rateLimit = t.middleware(async ({ ctx, next }) => {
	const identifier = ctx.user?.id || ctx.clientId;
	await rateLimitMiddleware(rateLimiters.api, identifier);
	return next();
});

// Auth rate limiting middleware (stricter)
const authRateLimit = t.middleware(async ({ ctx, next }) => {
	const identifier = ctx.clientId;
	await rateLimitMiddleware(rateLimiters.auth, identifier);
	return next();
});

export const publicProcedure = t.procedure.use(rateLimit);

// Middleware for authentication
const requireAuth = t.middleware(({ ctx, next }) => {
	if (!ctx.session || !ctx.user) {
		throw new TRPCError({
			code: 'UNAUTHORIZED',
			message: 'You must be logged in to access this resource'
		});
	}
	return next({
		ctx: {
			...ctx,
			session: ctx.session,
			user: ctx.user
		}
	});
});

// Middleware for admin access
const requireAdmin = t.middleware(({ ctx, next }) => {
	if (!ctx.session || !ctx.user) {
		throw new TRPCError({
			code: 'UNAUTHORIZED',
			message: 'You must be logged in to access this resource'
		});
	}
	
	if (ctx.user.role !== 'admin') {
		throw new TRPCError({
			code: 'FORBIDDEN',
			message: 'You must be an admin to access this resource'
		});
	}

	return next({
		ctx: {
			...ctx,
			session: ctx.session,
			user: ctx.user
		}
	});
});

// Procedure helpers
export const protectedProcedure = publicProcedure.use(requireAuth);
export const adminProcedure = publicProcedure.use(requireAdmin);
export const authProcedure = t.procedure.use(authRateLimit);

// Export the trpc instance for middleware use
export { t };