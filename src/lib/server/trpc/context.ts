import type { RequestEvent } from '@sveltejs/kit';
import { auth } from '../auth';
import { db } from '../db';
import { getClientId } from '../ratelimit';

export async function createContext(event: RequestEvent) {
	// Get session from auth
	const session = await auth.api.getSession({
		headers: event.request.headers
	});

	return {
		db,
		session,
		user: session?.user || null,
		request: event.request,
		url: event.url,
		clientId: getClientId(event.request)
	};
}

export type Context = Awaited<ReturnType<typeof createContext>>;