import { router } from './index';
import { usersRouter } from './routes/users';
import { organizationsRouter } from './routes/organizations';
import { subscriptionsRouter } from './routes/subscriptions';
import { billingRouter } from './routes/billing';

export const appRouter = router({
	users: usersRouter,
	organizations: organizationsRouter,
	subscriptions: subscriptionsRouter,
	billing: billingRouter
});

export type AppRouter = typeof appRouter;