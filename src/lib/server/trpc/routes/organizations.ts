import { z } from 'zod';
import { TRPCError } from '@trpc/server';
import { router, protectedProcedure, adminProcedure } from '../index';
import { organizations, organizationMembers, users, auditLogs } from '../../db/schema';
import { eq, and, desc, count, or, ilike } from 'drizzle-orm';

export const organizationsRouter = router({
	// Get current user's organizations
	mine: protectedProcedure.query(async ({ ctx }) => {
		const orgs = await ctx.db
			.select({
				id: organizations.id,
				name: organizations.name,
				slug: organizations.slug,
				description: organizations.description,
				image: organizations.image,
				ownerId: organizations.ownerId,
				createdAt: organizations.createdAt,
				role: organizationMembers.role
			})
			.from(organizations)
			.innerJoin(organizationMembers, eq(organizations.id, organizationMembers.organizationId))
			.where(
				and(
					eq(organizationMembers.userId, ctx.user.id),
					eq(organizations.deletedAt, null)
				)
			)
			.orderBy(desc(organizationMembers.joinedAt));

		return orgs;
	}),

	// Create new organization
	create: protectedProcedure
		.input(
			z.object({
				name: z.string().min(1).max(255),
				slug: z.string().min(1).max(255).regex(/^[a-z0-9-]+$/, 'Slug must contain only lowercase letters, numbers, and hyphens'),
				description: z.string().max(1000).optional()
			})
		)
		.mutation(async ({ ctx, input }) => {
			// Check if slug is unique
			const existingOrg = await ctx.db
				.select({ id: organizations.id })
				.from(organizations)
				.where(and(eq(organizations.slug, input.slug), eq(organizations.deletedAt, null)))
				.limit(1);

			if (existingOrg.length > 0) {
				throw new TRPCError({
					code: 'CONFLICT',
					message: 'Organization slug already exists'
				});
			}

			// Create organization
			const [newOrg] = await ctx.db
				.insert(organizations)
				.values({
					...input,
					ownerId: ctx.user.id
				})
				.returning();

			// Add creator as owner member
			await ctx.db.insert(organizationMembers).values({
				organizationId: newOrg.id,
				userId: ctx.user.id,
				role: 'owner'
			});

			// Log the action
			await ctx.db.insert(auditLogs).values({
				userId: ctx.user.id,
				action: 'create',
				resource: 'organization',
				resourceId: newOrg.id,
				metadata: { name: input.name, slug: input.slug, organizationId: newOrg.id },
				ipAddress: ctx.request.headers.get('x-forwarded-for') || 'unknown',
				userAgent: ctx.request.headers.get('user-agent') || 'unknown'
			});

			return newOrg;
		}),

	// Get organization by ID
	byId: protectedProcedure
		.input(z.object({ id: z.string() }))
		.query(async ({ ctx, input }) => {
			const org = await ctx.db
				.select({
					id: organizations.id,
					name: organizations.name,
					slug: organizations.slug,
					description: organizations.description,
					image: organizations.image,
					ownerId: organizations.ownerId,
					createdAt: organizations.createdAt,
					updatedAt: organizations.updatedAt,
					userRole: organizationMembers.role
				})
				.from(organizations)
				.innerJoin(organizationMembers, eq(organizations.id, organizationMembers.organizationId))
				.where(
					and(
						eq(organizations.id, input.id),
						eq(organizationMembers.userId, ctx.user.id),
						eq(organizations.deletedAt, null)
					)
				)
				.limit(1);

			if (!org[0]) {
				throw new TRPCError({
					code: 'NOT_FOUND',
					message: 'Organization not found or access denied'
				});
			}

			return org[0];
		}),

	// Update organization
	update: protectedProcedure
		.input(
			z.object({
				id: z.string(),
				name: z.string().min(1).max(255).optional(),
				description: z.string().max(1000).optional().nullable(),
				image: z.string().url().max(500).optional().nullable()
			})
		)
		.mutation(async ({ ctx, input }) => {
			const { id, ...updateData } = input;

			// Check if user has permission (owner or admin)
			const member = await ctx.db
				.select({ role: organizationMembers.role })
				.from(organizationMembers)
				.where(
					and(
						eq(organizationMembers.organizationId, id),
						eq(organizationMembers.userId, ctx.user.id)
					)
				)
				.limit(1);

			if (!member[0] || (member[0].role !== 'owner' && member[0].role !== 'admin')) {
				throw new TRPCError({
					code: 'FORBIDDEN',
					message: 'You must be an owner or admin to update this organization'
				});
			}

			const [updatedOrg] = await ctx.db
				.update(organizations)
				.set({
					...updateData,
					updatedAt: new Date()
				})
				.where(and(eq(organizations.id, id), eq(organizations.deletedAt, null)))
				.returning();

			if (!updatedOrg) {
				throw new TRPCError({
					code: 'NOT_FOUND',
					message: 'Organization not found'
				});
			}

			// Log the action
			await ctx.db.insert(auditLogs).values({
				userId: ctx.user.id,
				action: 'update',
				resource: 'organization',
				resourceId: id,
				metadata: { fields: Object.keys(updateData), organizationId: id },
				ipAddress: ctx.request.headers.get('x-forwarded-for') || 'unknown',
				userAgent: ctx.request.headers.get('user-agent') || 'unknown'
			});

			return updatedOrg;
		}),

	// Get organization members
	members: protectedProcedure
		.input(z.object({ organizationId: z.string() }))
		.query(async ({ ctx, input }) => {
			// Check if user is member of the organization
			const userMember = await ctx.db
				.select({ role: organizationMembers.role })
				.from(organizationMembers)
				.where(
					and(
						eq(organizationMembers.organizationId, input.organizationId),
						eq(organizationMembers.userId, ctx.user.id)
					)
				)
				.limit(1);

			if (!userMember[0]) {
				throw new TRPCError({
					code: 'FORBIDDEN',
					message: 'You are not a member of this organization'
				});
			}

			const members = await ctx.db
				.select({
					id: organizationMembers.id,
					role: organizationMembers.role,
					joinedAt: organizationMembers.joinedAt,
					user: {
						id: users.id,
						name: users.name,
						email: users.email,
						image: users.image
					}
				})
				.from(organizationMembers)
				.innerJoin(users, eq(organizationMembers.userId, users.id))
				.where(
					and(
						eq(organizationMembers.organizationId, input.organizationId),
						eq(organizationMembers.leftAt, null)
					)
				)
				.orderBy(desc(organizationMembers.joinedAt));

			return members;
		}),

	// Invite user to organization
	inviteUser: protectedProcedure
		.input(
			z.object({
				organizationId: z.string(),
				email: z.string().email(),
				role: z.enum(['member', 'admin']).default('member')
			})
		)
		.mutation(async ({ ctx, input }) => {
			// Check if user has permission (owner or admin)
			const userMember = await ctx.db
				.select({ role: organizationMembers.role })
				.from(organizationMembers)
				.where(
					and(
						eq(organizationMembers.organizationId, input.organizationId),
						eq(organizationMembers.userId, ctx.user.id)
					)
				)
				.limit(1);

			if (!userMember[0] || (userMember[0].role !== 'owner' && userMember[0].role !== 'admin')) {
				throw new TRPCError({
					code: 'FORBIDDEN',
					message: 'You must be an owner or admin to invite users'
				});
			}

			// Check if user exists
			const invitedUser = await ctx.db
				.select({ id: users.id })
				.from(users)
				.where(eq(users.email, input.email))
				.limit(1);

			if (!invitedUser[0]) {
				throw new TRPCError({
					code: 'NOT_FOUND',
					message: 'User with this email does not exist'
				});
			}

			// Check if user is already a member
			const existingMember = await ctx.db
				.select({ id: organizationMembers.id })
				.from(organizationMembers)
				.where(
					and(
						eq(organizationMembers.organizationId, input.organizationId),
						eq(organizationMembers.userId, invitedUser[0].id),
						eq(organizationMembers.leftAt, null)
					)
				)
				.limit(1);

			if (existingMember[0]) {
				throw new TRPCError({
					code: 'CONFLICT',
					message: 'User is already a member of this organization'
				});
			}

			// Add user as member
			const [newMember] = await ctx.db
				.insert(organizationMembers)
				.values({
					organizationId: input.organizationId,
					userId: invitedUser[0].id,
					role: input.role,
					invitedBy: ctx.user.id
				})
				.returning();

			// Log the action
			await ctx.db.insert(auditLogs).values({
				userId: ctx.user.id,
				action: 'invite_user',
				resource: 'organization_member',
				resourceId: newMember.id,
				metadata: { invitedEmail: input.email, role: input.role, organizationId: input.organizationId },
				ipAddress: ctx.request.headers.get('x-forwarded-for') || 'unknown',
				userAgent: ctx.request.headers.get('user-agent') || 'unknown'
			});

			return { success: true };
		}),

	// Remove user from organization
	removeMember: protectedProcedure
		.input(
			z.object({
				organizationId: z.string(),
				memberId: z.string()
			})
		)
		.mutation(async ({ ctx, input }) => {
			// Check if user has permission (owner or admin)
			const userMember = await ctx.db
				.select({ role: organizationMembers.role })
				.from(organizationMembers)
				.where(
					and(
						eq(organizationMembers.organizationId, input.organizationId),
						eq(organizationMembers.userId, ctx.user.id)
					)
				)
				.limit(1);

			if (!userMember[0] || (userMember[0].role !== 'owner' && userMember[0].role !== 'admin')) {
				throw new TRPCError({
					code: 'FORBIDDEN',
					message: 'You must be an owner or admin to remove members'
				});
			}

			// Get member to remove
			const memberToRemove = await ctx.db
				.select({ 
					id: organizationMembers.id, 
					userId: organizationMembers.userId,
					role: organizationMembers.role 
				})
				.from(organizationMembers)
				.where(
					and(
						eq(organizationMembers.id, input.memberId),
						eq(organizationMembers.organizationId, input.organizationId)
					)
				)
				.limit(1);

			if (!memberToRemove[0]) {
				throw new TRPCError({
					code: 'NOT_FOUND',
					message: 'Member not found'
				});
			}

			// Prevent owner from being removed
			if (memberToRemove[0].role === 'owner') {
				throw new TRPCError({
					code: 'BAD_REQUEST',
					message: 'Cannot remove organization owner'
				});
			}

			// Prevent users from removing themselves (use leave endpoint instead)
			if (memberToRemove[0].userId === ctx.user.id) {
				throw new TRPCError({
					code: 'BAD_REQUEST',
					message: 'Use the leave organization endpoint to remove yourself'
				});
			}

			// Mark member as left
			await ctx.db
				.update(organizationMembers)
				.set({ leftAt: new Date() })
				.where(eq(organizationMembers.id, input.memberId));

			// Log the action
			await ctx.db.insert(auditLogs).values({
				userId: ctx.user.id,
				action: 'remove_member',
				resource: 'organization_member',
				resourceId: input.memberId,
				metadata: { removedUserId: memberToRemove[0].userId, organizationId: input.organizationId },
				ipAddress: ctx.request.headers.get('x-forwarded-for') || 'unknown',
				userAgent: ctx.request.headers.get('user-agent') || 'unknown'
			});

			return { success: true };
		})
});