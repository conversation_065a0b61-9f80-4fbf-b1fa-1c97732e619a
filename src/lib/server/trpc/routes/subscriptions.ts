import { z } from 'zod';
import { TRPCError } from '@trpc/server';
import { router, protectedProcedure } from '../index';
import { subscriptions, organizations, organizationMembers, auditLogs } from '../../db/schema';
import { eq, and, desc } from 'drizzle-orm';

export const subscriptionsRouter = router({
	// Get organization subscription
	byOrganization: protectedProcedure
		.input(z.object({ organizationId: z.string() }))
		.query(async ({ ctx, input }) => {
			// Check if user is member of the organization
			const member = await ctx.db
				.select({ role: organizationMembers.role })
				.from(organizationMembers)
				.where(
					and(
						eq(organizationMembers.organizationId, input.organizationId),
						eq(organizationMembers.userId, ctx.user.id),
						eq(organizationMembers.leftAt, null)
					)
				)
				.limit(1);

			if (!member[0]) {
				throw new TRPCError({
					code: 'FORBIDDEN',
					message: 'You are not a member of this organization'
				});
			}

			const subscription = await ctx.db
				.select()
				.from(subscriptions)
				.where(eq(subscriptions.organizationId, input.organizationId))
				.orderBy(desc(subscriptions.createdAt))
				.limit(1);

			return subscription[0] || null;
		}),

	// Create subscription (placeholder - would integrate with Polar in production)
	create: protectedProcedure
		.input(
			z.object({
				organizationId: z.string(),
				plan: z.enum(['basic', 'pro', 'enterprise']),
				priceId: z.string()
			})
		)
		.mutation(async ({ ctx, input }) => {
			// Check if user is owner or admin of the organization
			const member = await ctx.db
				.select({ role: organizationMembers.role })
				.from(organizationMembers)
				.where(
					and(
						eq(organizationMembers.organizationId, input.organizationId),
						eq(organizationMembers.userId, ctx.user.id),
						eq(organizationMembers.leftAt, null)
					)
				)
				.limit(1);

			if (!member[0] || (member[0].role !== 'owner' && member[0].role !== 'admin')) {
				throw new TRPCError({
					code: 'FORBIDDEN',
					message: 'You must be an owner or admin to manage subscriptions'
				});
			}

			// Check if organization already has an active subscription
			const existingSubscription = await ctx.db
				.select()
				.from(subscriptions)
				.where(
					and(
						eq(subscriptions.organizationId, input.organizationId),
						eq(subscriptions.status, 'active')
					)
				)
				.limit(1);

			if (existingSubscription[0]) {
				throw new TRPCError({
					code: 'CONFLICT',
					message: 'Organization already has an active subscription'
				});
			}

			// In production, this would integrate with Polar API
			// For now, create a basic subscription record
			const [newSubscription] = await ctx.db
				.insert(subscriptions)
				.values({
					organizationId: input.organizationId,
					status: 'active',
					plan: input.plan,
					priceId: input.priceId,
					currentPeriodStart: new Date(),
					currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days from now
				})
				.returning();

			// Log the action
			await ctx.db.insert(auditLogs).values({
				userId: ctx.user.id,
				action: 'create',
				resource: 'subscription',
				resourceId: newSubscription.id,
				metadata: { plan: input.plan, priceId: input.priceId, organizationId: input.organizationId },
				ipAddress: ctx.request.headers.get('x-forwarded-for') || 'unknown',
				userAgent: ctx.request.headers.get('user-agent') || 'unknown'
			});

			return newSubscription;
		}),

	// Update subscription plan
	updatePlan: protectedProcedure
		.input(
			z.object({
				subscriptionId: z.string(),
				plan: z.enum(['basic', 'pro', 'enterprise']),
				priceId: z.string()
			})
		)
		.mutation(async ({ ctx, input }) => {
			// Get subscription and check permissions
			const subscription = await ctx.db
				.select({
					id: subscriptions.id,
					organizationId: subscriptions.organizationId,
					status: subscriptions.status
				})
				.from(subscriptions)
				.where(eq(subscriptions.id, input.subscriptionId))
				.limit(1);

			if (!subscription[0]) {
				throw new TRPCError({
					code: 'NOT_FOUND',
					message: 'Subscription not found'
				});
			}

			// Check if user is owner or admin of the organization
			const member = await ctx.db
				.select({ role: organizationMembers.role })
				.from(organizationMembers)
				.where(
					and(
						eq(organizationMembers.organizationId, subscription[0].organizationId),
						eq(organizationMembers.userId, ctx.user.id),
						eq(organizationMembers.leftAt, null)
					)
				)
				.limit(1);

			if (!member[0] || (member[0].role !== 'owner' && member[0].role !== 'admin')) {
				throw new TRPCError({
					code: 'FORBIDDEN',
					message: 'You must be an owner or admin to manage subscriptions'
				});
			}

			// In production, this would integrate with Polar API to update the subscription
			const [updatedSubscription] = await ctx.db
				.update(subscriptions)
				.set({
					plan: input.plan,
					priceId: input.priceId,
					updatedAt: new Date()
				})
				.where(eq(subscriptions.id, input.subscriptionId))
				.returning();

			// Log the action
			await ctx.db.insert(auditLogs).values({
				userId: ctx.user.id,
				action: 'update_plan',
				resource: 'subscription',
				resourceId: input.subscriptionId,
				metadata: { newPlan: input.plan, priceId: input.priceId, organizationId: subscription[0].organizationId },
				ipAddress: ctx.request.headers.get('x-forwarded-for') || 'unknown',
				userAgent: ctx.request.headers.get('user-agent') || 'unknown'
			});

			return updatedSubscription;
		}),

	// Cancel subscription
	cancel: protectedProcedure
		.input(z.object({ subscriptionId: z.string() }))
		.mutation(async ({ ctx, input }) => {
			// Get subscription and check permissions
			const subscription = await ctx.db
				.select({
					id: subscriptions.id,
					organizationId: subscriptions.organizationId,
					status: subscriptions.status
				})
				.from(subscriptions)
				.where(eq(subscriptions.id, input.subscriptionId))
				.limit(1);

			if (!subscription[0]) {
				throw new TRPCError({
					code: 'NOT_FOUND',
					message: 'Subscription not found'
				});
			}

			// Check if user is owner or admin of the organization
			const member = await ctx.db
				.select({ role: organizationMembers.role })
				.from(organizationMembers)
				.where(
					and(
						eq(organizationMembers.organizationId, subscription[0].organizationId),
						eq(organizationMembers.userId, ctx.user.id),
						eq(organizationMembers.leftAt, null)
					)
				)
				.limit(1);

			if (!member[0] || (member[0].role !== 'owner' && member[0].role !== 'admin')) {
				throw new TRPCError({
					code: 'FORBIDDEN',
					message: 'You must be an owner or admin to manage subscriptions'
				});
			}

			// In production, this would integrate with Polar API to cancel the subscription
			const [canceledSubscription] = await ctx.db
				.update(subscriptions)
				.set({
					status: 'canceled',
					cancelAt: new Date(),
					updatedAt: new Date()
				})
				.where(eq(subscriptions.id, input.subscriptionId))
				.returning();

			// Log the action
			await ctx.db.insert(auditLogs).values({
				userId: ctx.user.id,
				action: 'cancel',
				resource: 'subscription',
				resourceId: input.subscriptionId,
				metadata: { organizationId: subscription[0].organizationId },
				ipAddress: ctx.request.headers.get('x-forwarded-for') || 'unknown',
				userAgent: ctx.request.headers.get('user-agent') || 'unknown'
			});

			return canceledSubscription;
		}),

	// Get subscription usage
	usage: protectedProcedure
		.input(
			z.object({
				organizationId: z.string(),
				period: z.string().optional() // YYYY-MM format, defaults to current month
			})
		)
		.query(async ({ ctx, input }) => {
			// Check if user is member of the organization
			const member = await ctx.db
				.select({ role: organizationMembers.role })
				.from(organizationMembers)
				.where(
					and(
						eq(organizationMembers.organizationId, input.organizationId),
						eq(organizationMembers.userId, ctx.user.id),
						eq(organizationMembers.leftAt, null)
					)
				)
				.limit(1);

			if (!member[0]) {
				throw new TRPCError({
					code: 'FORBIDDEN',
					message: 'You are not a member of this organization'
				});
			}

			const period = input.period || new Date().toISOString().slice(0, 7); // YYYY-MM

			// In production, this would calculate actual usage from usage_records table
			// For now, return mock data
			return {
				period,
				apiCalls: 1250,
				storageGb: 2.5,
				users: 5,
				limits: {
					apiCalls: 10000,
					storageGb: 10,
					users: 10
				}
			};
		})
});