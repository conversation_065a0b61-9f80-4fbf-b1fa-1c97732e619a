import { z } from 'zod';
import { TRPCError } from '@trpc/server';
import { router, protectedProcedure } from '../index';
import { subscriptions, organizations, organizationMembers, invoices, auditLogs } from '../../db/schema';
import { eq, and, desc } from 'drizzle-orm';
import { env, features } from '../../env';

// Mock Polar API functions - replace with actual Polar SDK in production
const createPolarSubscription = async (data: {
	organizationId: string;
	plan: string;
	priceId: string;
}) => {
	if (!features.polar) {
		throw new Error('Polar integration not configured');
	}
	
	// Mock response - replace with actual Polar API call
	return {
		id: `polar_sub_${Date.now()}`,
		status: 'active',
		currentPeriodStart: new Date(),
		currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
	};
};

const updatePolarSubscription = async (subscriptionId: string, data: {
	plan: string;
	priceId: string;
}) => {
	if (!features.polar) {
		throw new Error('Polar integration not configured');
	}
	
	// Mock response - replace with actual Polar API call
	return {
		id: subscriptionId,
		status: 'active'
	};
};

const cancelPolarSubscription = async (subscriptionId: string) => {
	if (!features.polar) {
		throw new Error('Polar integration not configured');
	}
	
	// Mock response - replace with actual Polar API call
	return {
		id: subscriptionId,
		status: 'canceled',
		canceledAt: new Date()
	};
};

const createPolarCheckoutSession = async (data: {
	priceId: string;
	organizationId: string;
	successUrl: string;
	cancelUrl: string;
}) => {
	if (!features.polar) {
		throw new Error('Polar integration not configured');
	}
	
	// Mock response - replace with actual Polar API call
	return {
		url: `https://checkout.polar.dev/session_${Date.now()}`,
		id: `cs_${Date.now()}`
	};
};

export const billingRouter = router({
	// Get available subscription plans
	plans: protectedProcedure.query(async () => {
		// In production, this might come from Polar API or database
		return [
			{
				id: 'basic',
				name: 'Basic',
				description: 'Perfect for getting started',
				price: 29,
				currency: 'USD',
				interval: 'month',
				features: [
					'Up to 5 team members',
					'10,000 API calls/month',
					'5GB storage',
					'Email support'
				],
				priceId: 'price_basic_monthly'
			},
			{
				id: 'pro',
				name: 'Professional',
				description: 'Best for growing teams',
				price: 99,
				currency: 'USD',
				interval: 'month',
				features: [
					'Up to 20 team members',
					'100,000 API calls/month',
					'50GB storage',
					'Priority support',
					'Advanced analytics'
				],
				priceId: 'price_pro_monthly'
			},
			{
				id: 'enterprise',
				name: 'Enterprise',
				description: 'For large organizations',
				price: 299,
				currency: 'USD',
				interval: 'month',
				features: [
					'Unlimited team members',
					'Unlimited API calls',
					'500GB storage',
					'24/7 dedicated support',
					'Custom integrations',
					'SLA guarantee'
				],
				priceId: 'price_enterprise_monthly'
			}
		];
	}),

	// Create checkout session for new subscription
	createCheckout: protectedProcedure
		.input(
			z.object({
				organizationId: z.string(),
				priceId: z.string(),
				successUrl: z.string().url(),
				cancelUrl: z.string().url()
			})
		)
		.mutation(async ({ ctx, input }) => {
			// Check if user has permission (owner or admin)
			const member = await ctx.db
				.select({ role: organizationMembers.role })
				.from(organizationMembers)
				.where(
					and(
						eq(organizationMembers.organizationId, input.organizationId),
						eq(organizationMembers.userId, ctx.user.id),
						eq(organizationMembers.leftAt, null)
					)
				)
				.limit(1);

			if (!member[0] || (member[0].role !== 'owner' && member[0].role !== 'admin')) {
				throw new TRPCError({
					code: 'FORBIDDEN',
					message: 'You must be an owner or admin to manage billing'
				});
			}

			// Check if organization already has an active subscription
			const existingSubscription = await ctx.db
				.select()
				.from(subscriptions)
				.where(
					and(
						eq(subscriptions.organizationId, input.organizationId),
						eq(subscriptions.status, 'active')
					)
				)
				.limit(1);

			if (existingSubscription[0]) {
				throw new TRPCError({
					code: 'CONFLICT',
					message: 'Organization already has an active subscription'
				});
			}

			try {
				const checkoutSession = await createPolarCheckoutSession({
					priceId: input.priceId,
					organizationId: input.organizationId,
					successUrl: input.successUrl,
					cancelUrl: input.cancelUrl
				});

				// Log the action
				await ctx.db.insert(auditLogs).values({
					userId: ctx.user.id,
					action: 'create_checkout',
					resource: 'billing',
					resourceId: checkoutSession.id,
					metadata: { priceId: input.priceId, organizationId: input.organizationId },
					ipAddress: ctx.request.headers.get('x-forwarded-for') || 'unknown',
					userAgent: ctx.request.headers.get('user-agent') || 'unknown'
				});

				return checkoutSession;
			} catch (error) {
				throw new TRPCError({
					code: 'INTERNAL_SERVER_ERROR',
					message: 'Failed to create checkout session'
				});
			}
		}),

	// Get organization's current subscription
	currentSubscription: protectedProcedure
		.input(z.object({ organizationId: z.string() }))
		.query(async ({ ctx, input }) => {
			// Check if user is member of the organization
			const member = await ctx.db
				.select({ role: organizationMembers.role })
				.from(organizationMembers)
				.where(
					and(
						eq(organizationMembers.organizationId, input.organizationId),
						eq(organizationMembers.userId, ctx.user.id),
						eq(organizationMembers.leftAt, null)
					)
				)
				.limit(1);

			if (!member[0]) {
				throw new TRPCError({
					code: 'FORBIDDEN',
					message: 'You are not a member of this organization'
				});
			}

			const subscription = await ctx.db
				.select()
				.from(subscriptions)
				.where(eq(subscriptions.organizationId, input.organizationId))
				.orderBy(desc(subscriptions.createdAt))
				.limit(1);

			return subscription[0] || null;
		}),

	// Get billing history (invoices)
	invoices: protectedProcedure
		.input(
			z.object({
				organizationId: z.string(),
				limit: z.number().min(1).max(50).default(20),
				offset: z.number().min(0).default(0)
			})
		)
		.query(async ({ ctx, input }) => {
			// Check if user is member of the organization
			const member = await ctx.db
				.select({ role: organizationMembers.role })
				.from(organizationMembers)
				.where(
					and(
						eq(organizationMembers.organizationId, input.organizationId),
						eq(organizationMembers.userId, ctx.user.id),
						eq(organizationMembers.leftAt, null)
					)
				)
				.limit(1);

			if (!member[0]) {
				throw new TRPCError({
					code: 'FORBIDDEN',
					message: 'You are not a member of this organization'
				});
			}

			const organizationInvoices = await ctx.db
				.select()
				.from(invoices)
				.where(eq(invoices.organizationId, input.organizationId))
				.orderBy(desc(invoices.createdAt))
				.limit(input.limit)
				.offset(input.offset);

			return organizationInvoices;
		}),

	// Update subscription plan
	updatePlan: protectedProcedure
		.input(
			z.object({
				subscriptionId: z.string(),
				priceId: z.string(),
				plan: z.enum(['basic', 'pro', 'enterprise'])
			})
		)
		.mutation(async ({ ctx, input }) => {
			// Get subscription and check permissions
			const subscription = await ctx.db
				.select({
					id: subscriptions.id,
					organizationId: subscriptions.organizationId,
					polarSubscriptionId: subscriptions.polarSubscriptionId,
					status: subscriptions.status
				})
				.from(subscriptions)
				.where(eq(subscriptions.id, input.subscriptionId))
				.limit(1);

			if (!subscription[0]) {
				throw new TRPCError({
					code: 'NOT_FOUND',
					message: 'Subscription not found'
				});
			}

			// Check if user has permission
			const member = await ctx.db
				.select({ role: organizationMembers.role })
				.from(organizationMembers)
				.where(
					and(
						eq(organizationMembers.organizationId, subscription[0].organizationId),
						eq(organizationMembers.userId, ctx.user.id),
						eq(organizationMembers.leftAt, null)
					)
				)
				.limit(1);

			if (!member[0] || (member[0].role !== 'owner' && member[0].role !== 'admin')) {
				throw new TRPCError({
					code: 'FORBIDDEN',
					message: 'You must be an owner or admin to manage subscriptions'
				});
			}

			try {
				// Update subscription with Polar
				if (subscription[0].polarSubscriptionId) {
					await updatePolarSubscription(subscription[0].polarSubscriptionId, {
						plan: input.plan,
						priceId: input.priceId
					});
				}

				// Update local subscription record
				const [updatedSubscription] = await ctx.db
					.update(subscriptions)
					.set({
						plan: input.plan,
						priceId: input.priceId,
						updatedAt: new Date()
					})
					.where(eq(subscriptions.id, input.subscriptionId))
					.returning();

				// Log the action
				await ctx.db.insert(auditLogs).values({
					userId: ctx.user.id,
					action: 'update_plan',
					resource: 'subscription',
					resourceId: input.subscriptionId,
					metadata: { newPlan: input.plan, priceId: input.priceId, organizationId: subscription[0].organizationId },
					ipAddress: ctx.request.headers.get('x-forwarded-for') || 'unknown',
					userAgent: ctx.request.headers.get('user-agent') || 'unknown'
				});

				return updatedSubscription;
			} catch (error) {
				throw new TRPCError({
					code: 'INTERNAL_SERVER_ERROR',
					message: 'Failed to update subscription'
				});
			}
		}),

	// Cancel subscription
	cancelSubscription: protectedProcedure
		.input(z.object({ subscriptionId: z.string() }))
		.mutation(async ({ ctx, input }) => {
			// Get subscription and check permissions
			const subscription = await ctx.db
				.select({
					id: subscriptions.id,
					organizationId: subscriptions.organizationId,
					polarSubscriptionId: subscriptions.polarSubscriptionId,
					status: subscriptions.status
				})
				.from(subscriptions)
				.where(eq(subscriptions.id, input.subscriptionId))
				.limit(1);

			if (!subscription[0]) {
				throw new TRPCError({
					code: 'NOT_FOUND',
					message: 'Subscription not found'
				});
			}

			// Check if user has permission
			const member = await ctx.db
				.select({ role: organizationMembers.role })
				.from(organizationMembers)
				.where(
					and(
						eq(organizationMembers.organizationId, subscription[0].organizationId),
						eq(organizationMembers.userId, ctx.user.id),
						eq(organizationMembers.leftAt, null)
					)
				)
				.limit(1);

			if (!member[0] || (member[0].role !== 'owner' && member[0].role !== 'admin')) {
				throw new TRPCError({
					code: 'FORBIDDEN',
					message: 'You must be an owner or admin to manage subscriptions'
				});
			}

			try {
				// Cancel subscription with Polar
				if (subscription[0].polarSubscriptionId) {
					await cancelPolarSubscription(subscription[0].polarSubscriptionId);
				}

				// Update local subscription record
				const [canceledSubscription] = await ctx.db
					.update(subscriptions)
					.set({
						status: 'canceled',
						cancelAt: new Date(),
						updatedAt: new Date()
					})
					.where(eq(subscriptions.id, input.subscriptionId))
					.returning();

				// Log the action
				await ctx.db.insert(auditLogs).values({
					userId: ctx.user.id,
					action: 'cancel_subscription',
					resource: 'subscription',
					resourceId: input.subscriptionId,
					metadata: { organizationId: subscription[0].organizationId },
					ipAddress: ctx.request.headers.get('x-forwarded-for') || 'unknown',
					userAgent: ctx.request.headers.get('user-agent') || 'unknown'
				});

				return canceledSubscription;
			} catch (error) {
				throw new TRPCError({
					code: 'INTERNAL_SERVER_ERROR',
					message: 'Failed to cancel subscription'
				});
			}
		}),

	// Get usage for current billing period
	usage: protectedProcedure
		.input(z.object({ organizationId: z.string() }))
		.query(async ({ ctx, input }) => {
			// Check if user is member of the organization
			const member = await ctx.db
				.select({ role: organizationMembers.role })
				.from(organizationMembers)
				.where(
					and(
						eq(organizationMembers.organizationId, input.organizationId),
						eq(organizationMembers.userId, ctx.user.id),
						eq(organizationMembers.leftAt, null)
					)
				)
				.limit(1);

			if (!member[0]) {
				throw new TRPCError({
					code: 'FORBIDDEN',
					message: 'You are not a member of this organization'
				});
			}

			// In production, this would query the usage_records table
			// For now, return mock data
			return {
				apiCalls: {
					current: 5420,
					limit: 10000,
					percentage: 54.2
				},
				storage: {
					current: 2.1, // GB
					limit: 10,
					percentage: 21.0
				},
				teamMembers: {
					current: 3,
					limit: 5,
					percentage: 60.0
				}
			};
		})
});