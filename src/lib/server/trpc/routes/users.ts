import { z } from 'zod';
import { TRPCError } from '@trpc/server';
import { router, protectedProcedure, adminProcedure, publicProcedure } from '../index';
import { users, auditLogs } from '../../db/schema';
import { eq, and, desc, count, or, ilike } from 'drizzle-orm';

export const usersRouter = router({
	// Get current user profile
	me: protectedProcedure.query(async ({ ctx }) => {
		const user = await ctx.db
			.select()
			.from(users)
			.where(eq(users.id, ctx.user.id))
			.limit(1);

		if (!user[0]) {
			throw new TRPCError({
				code: 'NOT_FOUND',
				message: 'User not found'
			});
		}

		return user[0];
	}),

	// Update user profile
	updateProfile: protectedProcedure
		.input(
			z.object({
				name: z.string().min(1).max(255).optional(),
				image: z.string().url().max(500).optional().nullable()
			})
		)
		.mutation(async ({ ctx, input }) => {
			const [updatedUser] = await ctx.db
				.update(users)
				.set({
					...input,
					updatedAt: new Date()
				})
				.where(eq(users.id, ctx.user.id))
				.returning();

			// Log the action
			await ctx.db.insert(auditLogs).values({
				userId: ctx.user.id,
				action: 'update',
				resource: 'user',
				resourceId: ctx.user.id,
				metadata: { fields: Object.keys(input) },
				ipAddress: ctx.request.headers.get('x-forwarded-for') || ctx.request.headers.get('cf-connecting-ip') || 'unknown',
				userAgent: ctx.request.headers.get('user-agent') || 'unknown'
			});

			return updatedUser;
		}),

	// Update last login timestamp
	updateLastLogin: protectedProcedure.mutation(async ({ ctx }) => {
		await ctx.db
			.update(users)
			.set({
				lastLoginAt: new Date(),
				updatedAt: new Date()
			})
			.where(eq(users.id, ctx.user.id));

		return { success: true };
	}),

	// Admin: List all users with pagination
	list: adminProcedure
		.input(
			z.object({
				page: z.number().min(1).default(1),
				limit: z.number().min(1).max(100).default(20),
				search: z.string().optional(),
				role: z.enum(['user', 'admin']).optional(),
				isActive: z.boolean().optional()
			})
		)
		.query(async ({ ctx, input }) => {
			const { page, limit, search, role, isActive } = input;
			const offset = (page - 1) * limit;

			// Build where conditions
			const conditions = [];
			
			if (search) {
				// Note: This is a simple implementation. In production, consider using full-text search
				conditions.push(
					// @ts-ignore - Drizzle types for ilike might not be perfect
					or(
						ilike(users.name, `%${search}%`),
						ilike(users.email, `%${search}%`)
					)
				);
			}
			
			if (role) {
				conditions.push(eq(users.role, role));
			}
			
			if (typeof isActive === 'boolean') {
				conditions.push(eq(users.isActive, isActive));
			}

			// Only include non-deleted users
			conditions.push(eq(users.deletedAt, null));

			const whereClause = conditions.length > 0 ? and(...conditions) : undefined;

			// Get users
			const usersList = await ctx.db
				.select({
					id: users.id,
					email: users.email,
					name: users.name,
					image: users.image,
					role: users.role,
					isActive: users.isActive,
					lastLoginAt: users.lastLoginAt,
					createdAt: users.createdAt,
					updatedAt: users.updatedAt
				})
				.from(users)
				.where(whereClause)
				.orderBy(desc(users.createdAt))
				.limit(limit)
				.offset(offset);

			// Get total count
			const [{ total }] = await ctx.db
				.select({ total: count() })
				.from(users)
				.where(whereClause);

			return {
				users: usersList,
				pagination: {
					page,
					limit,
					total,
					totalPages: Math.ceil(total / limit)
				}
			};
		}),

	// Admin: Update user role
	updateRole: adminProcedure
		.input(
			z.object({
				userId: z.string(),
				role: z.enum(['user', 'admin'])
			})
		)
		.mutation(async ({ ctx, input }) => {
			const { userId, role } = input;

			// Prevent admin from demoting themselves
			if (userId === ctx.user.id && role !== 'admin') {
				throw new TRPCError({
					code: 'BAD_REQUEST',
					message: 'You cannot change your own admin role'
				});
			}

			const [updatedUser] = await ctx.db
				.update(users)
				.set({
					role,
					updatedAt: new Date()
				})
				.where(and(eq(users.id, userId), eq(users.deletedAt, null)))
				.returning();

			if (!updatedUser) {
				throw new TRPCError({
					code: 'NOT_FOUND',
					message: 'User not found'
				});
			}

			// Log the action
			await ctx.db.insert(auditLogs).values({
				userId: ctx.user.id,
				action: 'update_role',
				resource: 'user',
				resourceId: userId,
				metadata: { newRole: role, previousRole: updatedUser.role },
				ipAddress: ctx.request.headers.get('x-forwarded-for') || ctx.request.headers.get('cf-connecting-ip') || 'unknown',
				userAgent: ctx.request.headers.get('user-agent') || 'unknown'
			});

			return updatedUser;
		}),

	// Admin: Deactivate user (soft delete)
	deactivate: adminProcedure
		.input(z.object({ userId: z.string() }))
		.mutation(async ({ ctx, input }) => {
			const { userId } = input;

			// Prevent admin from deactivating themselves
			if (userId === ctx.user.id) {
				throw new TRPCError({
					code: 'BAD_REQUEST',
					message: 'You cannot deactivate your own account'
				});
			}

			const [deactivatedUser] = await ctx.db
				.update(users)
				.set({
					isActive: false,
					updatedAt: new Date()
				})
				.where(and(eq(users.id, userId), eq(users.deletedAt, null)))
				.returning();

			if (!deactivatedUser) {
				throw new TRPCError({
					code: 'NOT_FOUND',
					message: 'User not found'
				});
			}

			// Log the action
			await ctx.db.insert(auditLogs).values({
				userId: ctx.user.id,
				action: 'deactivate',
				resource: 'user',
				resourceId: userId,
				ipAddress: ctx.request.headers.get('x-forwarded-for') || ctx.request.headers.get('cf-connecting-ip') || 'unknown',
				userAgent: ctx.request.headers.get('user-agent') || 'unknown'
			});

			return deactivatedUser;
		}),

	// Get user statistics (public for dashboard)
	stats: publicProcedure.query(async ({ ctx }) => {
		const [totalUsers] = await ctx.db
			.select({ count: count() })
			.from(users)
			.where(and(eq(users.deletedAt, null), eq(users.isActive, true)));

		const [totalAdmins] = await ctx.db
			.select({ count: count() })
			.from(users)
			.where(and(
				eq(users.deletedAt, null), 
				eq(users.isActive, true),
				eq(users.role, 'admin')
			));

		return {
			totalUsers: totalUsers.count,
			totalAdmins: totalAdmins.count
		};
	})
});