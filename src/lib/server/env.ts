import { z } from 'zod';

const envSchema = z.object({
	// Database
	DATABASE_URL: z.string().min(1),
	
	// Authentication
	AUTH_SECRET: z.string().min(1),
	AUTH_TRUST_HOST: z.enum(['true', 'false']).optional().default('true'),
	
	// OAuth Providers (optional)
	GOOGLE_CLIENT_ID: z.string().optional(),
	GOOGLE_CLIENT_SECRET: z.string().optional(),
	GITHUB_CLIENT_ID: z.string().optional(),
	GITHUB_CLIENT_SECRET: z.string().optional(),
	
	// Email (optional)
	SMTP_HOST: z.string().optional(),
	SMTP_PORT: z.string().optional(),
	SMTP_USER: z.string().optional(),
	SMTP_PASS: z.string().optional(),
	FROM_EMAIL: z.string().email().optional(),
	
	// Polar Payments (optional)
	POLAR_ACCESS_TOKEN: z.string().optional(),
	POLAR_WEBHOOK_SECRET: z.string().optional(),
	POLAR_ORGANIZATION_ID: z.string().optional(),
	
	// OpenAI (optional)
	OPENAI_API_KEY: z.string().optional(),
	OPENAI_ORGANIZATION: z.string().optional(),
	
	// Inngest (optional)
	INNGEST_EVENT_KEY: z.string().optional(),
	INNGEST_SIGNING_KEY: z.string().optional(),
	
	// Application
	PUBLIC_APP_URL: z.string().url().optional().default('http://localhost:5173'),
	APP_ENV: z.enum(['development', 'staging', 'production']).optional().default('development'),
	
	// Analytics (optional)
	GOOGLE_ANALYTICS_ID: z.string().optional(),
	POSTHOG_KEY: z.string().optional(),
	
	// Error Tracking (optional)
	SENTRY_DSN: z.string().url().optional(),
	
	// Redis (optional)
	REDIS_URL: z.string().url().optional(),
	
	// File Storage (optional)
	AWS_ACCESS_KEY_ID: z.string().optional(),
	AWS_SECRET_ACCESS_KEY: z.string().optional(),
	AWS_S3_BUCKET: z.string().optional(),
	AWS_REGION: z.string().optional().default('us-east-1')
});

function validateEnv() {
	try {
		const parsed = envSchema.parse(process.env);
		return parsed;
	} catch (error) {
		if (error instanceof z.ZodError) {
			console.error('❌ Environment validation failed:');
			error.errors.forEach((err) => {
				console.error(`  ${err.path.join('.')}: ${err.message}`);
			});
			
			// During build or development, use default values if missing
			if (process.env.NODE_ENV === 'production' || process.env.VITE_BUILD_MODE || process.env.NODE_ENV === 'development') {
				console.warn('Using default values for missing environment variables');
				return {
					DATABASE_URL: process.env.DATABASE_URL || 'postgresql://user:password@localhost:5432/database',
					AUTH_SECRET: process.env.AUTH_SECRET || 'default-auth-secret-for-development-only-not-for-production-use',
					AUTH_TRUST_HOST: 'true',
					PUBLIC_APP_URL: 'http://localhost:5173',
					APP_ENV: 'development'
				} as any;
			}
			
			process.exit(1);
		}
		throw error;
	}
}

export const env = validateEnv();

// Helper to check if feature is enabled
export const features = {
	email: !!(env.SMTP_HOST && env.SMTP_USER && env.SMTP_PASS),
	polar: !!(env.POLAR_ACCESS_TOKEN && env.POLAR_ORGANIZATION_ID),
	openai: !!env.OPENAI_API_KEY,
	inngest: !!(env.INNGEST_EVENT_KEY && env.INNGEST_SIGNING_KEY),
	google: !!(env.GOOGLE_CLIENT_ID && env.GOOGLE_CLIENT_SECRET),
	github: !!(env.GITHUB_CLIENT_ID && env.GITHUB_CLIENT_SECRET),
	analytics: !!(env.GOOGLE_ANALYTICS_ID || env.POSTHOG_KEY),
	sentry: !!env.SENTRY_DSN,
	redis: !!env.REDIS_URL,
	s3: !!(env.AWS_ACCESS_KEY_ID && env.AWS_SECRET_ACCESS_KEY && env.AWS_S3_BUCKET)
} as const;

export type Features = typeof features;