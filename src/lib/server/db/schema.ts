import { pgTable, varchar, text, timestamp, boolean, integer, decimal, json } from 'drizzle-orm/pg-core';
import { createId } from '@paralleldrive/cuid2';

export const users = pgTable('users', {
	id: varchar('id', { length: 128 }).primaryKey().$defaultFn(() => createId()),
	email: varchar('email', { length: 255 }).notNull().unique(),
	emailVerified: timestamp('email_verified'),
	name: varchar('name', { length: 255 }),
	image: varchar('image', { length: 500 }),
	password: varchar('password', { length: 255 }), // For email/password auth
	role: varchar('role', { length: 50 }).default('user').notNull(),
	createdAt: timestamp('created_at').defaultNow().notNull(),
	updatedAt: timestamp('updated_at').defaultNow().notNull(),
	lastLoginAt: timestamp('last_login_at'),
	isActive: boolean('is_active').default(true).notNull(),
	deletedAt: timestamp('deleted_at')
});

export const accounts = pgTable('accounts', {
	id: varchar('id', { length: 128 }).primaryKey().$defaultFn(() => createId()),
	userId: varchar('user_id', { length: 128 }).notNull().references(() => users.id, { onDelete: 'cascade' }),
	type: varchar('type', { length: 255 }).notNull(),
	provider: varchar('provider', { length: 255 }).notNull(),
	providerAccountId: varchar('provider_account_id', { length: 255 }).notNull(),
	refreshToken: text('refresh_token'),
	accessToken: text('access_token'),
	expiresAt: integer('expires_at'),
	tokenType: varchar('token_type', { length: 255 }),
	scope: varchar('scope', { length: 255 }),
	idToken: text('id_token'),
	sessionState: varchar('session_state', { length: 255 }),
	createdAt: timestamp('created_at').defaultNow().notNull(),
	updatedAt: timestamp('updated_at').defaultNow().notNull()
});

export const sessions = pgTable('sessions', {
	id: varchar('id', { length: 128 }).primaryKey().$defaultFn(() => createId()),
	sessionToken: varchar('session_token', { length: 255 }).notNull().unique(),
	userId: varchar('user_id', { length: 128 }).notNull().references(() => users.id, { onDelete: 'cascade' }),
	expires: timestamp('expires').notNull(),
	createdAt: timestamp('created_at').defaultNow().notNull()
});

export const verificationTokens = pgTable('verification', {
	id: varchar('id', { length: 128 }).primaryKey().$defaultFn(() => createId()),
	identifier: varchar('identifier', { length: 255 }).notNull(),
	value: varchar('value', { length: 255 }).notNull(),
	expiresAt: timestamp('expires_at').notNull(),
	createdAt: timestamp('created_at').defaultNow().notNull(),
	updatedAt: timestamp('updated_at').defaultNow().notNull()
});

export const organizations = pgTable('organizations', {
	id: varchar('id', { length: 128 }).primaryKey().$defaultFn(() => createId()),
	name: varchar('name', { length: 255 }).notNull(),
	slug: varchar('slug', { length: 255 }).notNull().unique(),
	description: text('description'),
	image: varchar('image', { length: 500 }),
	ownerId: varchar('owner_id', { length: 128 }).notNull().references(() => users.id),
	createdAt: timestamp('created_at').defaultNow().notNull(),
	updatedAt: timestamp('updated_at').defaultNow().notNull(),
	deletedAt: timestamp('deleted_at')
});

export const organizationMembers = pgTable('organization_members', {
	id: varchar('id', { length: 128 }).primaryKey().$defaultFn(() => createId()),
	organizationId: varchar('organization_id', { length: 128 }).notNull().references(() => organizations.id, { onDelete: 'cascade' }),
	userId: varchar('user_id', { length: 128 }).notNull().references(() => users.id, { onDelete: 'cascade' }),
	role: varchar('role', { length: 50 }).default('member').notNull(), // owner, admin, member
	invitedBy: varchar('invited_by', { length: 128 }).references(() => users.id),
	joinedAt: timestamp('joined_at').defaultNow().notNull(),
	leftAt: timestamp('left_at')
});

export const subscriptions = pgTable('subscriptions', {
	id: varchar('id', { length: 128 }).primaryKey().$defaultFn(() => createId()),
	organizationId: varchar('organization_id', { length: 128 }).notNull().references(() => organizations.id, { onDelete: 'cascade' }),
	polarSubscriptionId: varchar('polar_subscription_id', { length: 255 }).unique(),
	status: varchar('status', { length: 50 }).notNull(), // active, canceled, past_due, incomplete, trialing
	plan: varchar('plan', { length: 100 }).notNull(), // basic, pro, enterprise
	priceId: varchar('price_id', { length: 255 }),
	quantity: integer('quantity').default(1).notNull(),
	currentPeriodStart: timestamp('current_period_start'),
	currentPeriodEnd: timestamp('current_period_end'),
	trialStart: timestamp('trial_start'),
	trialEnd: timestamp('trial_end'),
	cancelAt: timestamp('cancel_at'),
	canceledAt: timestamp('canceled_at'),
	createdAt: timestamp('created_at').defaultNow().notNull(),
	updatedAt: timestamp('updated_at').defaultNow().notNull()
});

export const invoices = pgTable('invoices', {
	id: varchar('id', { length: 128 }).primaryKey().$defaultFn(() => createId()),
	organizationId: varchar('organization_id', { length: 128 }).notNull().references(() => organizations.id, { onDelete: 'cascade' }),
	subscriptionId: varchar('subscription_id', { length: 128 }).references(() => subscriptions.id),
	polarInvoiceId: varchar('polar_invoice_id', { length: 255 }).unique(),
	status: varchar('status', { length: 50 }).notNull(), // paid, open, void, uncollectible
	amountPaid: decimal('amount_paid', { precision: 10, scale: 2 }),
	amountDue: decimal('amount_due', { precision: 10, scale: 2 }),
	currency: varchar('currency', { length: 3 }).default('USD'),
	periodStart: timestamp('period_start'),
	periodEnd: timestamp('period_end'),
	dueDate: timestamp('due_date'),
	paidAt: timestamp('paid_at'),
	createdAt: timestamp('created_at').defaultNow().notNull(),
	updatedAt: timestamp('updated_at').defaultNow().notNull()
});

export const usageRecords = pgTable('usage_records', {
	id: varchar('id', { length: 128 }).primaryKey().$defaultFn(() => createId()),
	organizationId: varchar('organization_id', { length: 128 }).notNull().references(() => organizations.id, { onDelete: 'cascade' }),
	feature: varchar('feature', { length: 100 }).notNull(), // api_calls, storage_gb, users
	quantity: integer('quantity').notNull(),
	period: varchar('period', { length: 7 }).notNull(), // YYYY-MM format
	recordedAt: timestamp('recorded_at').defaultNow().notNull()
});

export const apiKeys = pgTable('api_keys', {
	id: varchar('id', { length: 128 }).primaryKey().$defaultFn(() => createId()),
	organizationId: varchar('organization_id', { length: 128 }).notNull().references(() => organizations.id, { onDelete: 'cascade' }),
	name: varchar('name', { length: 255 }).notNull(),
	keyHash: varchar('key_hash', { length: 255 }).notNull().unique(),
	prefix: varchar('prefix', { length: 10 }).notNull(),
	lastUsedAt: timestamp('last_used_at'),
	expiresAt: timestamp('expires_at'),
	isActive: boolean('is_active').default(true).notNull(),
	createdBy: varchar('created_by', { length: 128 }).notNull().references(() => users.id),
	createdAt: timestamp('created_at').defaultNow().notNull(),
	updatedAt: timestamp('updated_at').defaultNow().notNull()
});

export const auditLogs = pgTable('audit_logs', {
	id: varchar('id', { length: 128 }).primaryKey().$defaultFn(() => createId()),
	organizationId: varchar('organization_id', { length: 128 }).references(() => organizations.id),
	userId: varchar('user_id', { length: 128 }).references(() => users.id),
	action: varchar('action', { length: 100 }).notNull(),
	resource: varchar('resource', { length: 100 }).notNull(),
	resourceId: varchar('resource_id', { length: 128 }),
	metadata: json('metadata'),
	ipAddress: varchar('ip_address', { length: 45 }),
	userAgent: text('user_agent'),
	createdAt: timestamp('created_at').defaultNow().notNull()
});

export const notifications = pgTable('notifications', {
	id: varchar('id', { length: 128 }).primaryKey().$defaultFn(() => createId()),
	userId: varchar('user_id', { length: 128 }).notNull().references(() => users.id, { onDelete: 'cascade' }),
	title: varchar('title', { length: 255 }).notNull(),
	message: text('message').notNull(),
	type: varchar('type', { length: 50 }).notNull(), // info, success, warning, error
	isRead: boolean('is_read').default(false).notNull(),
	actionUrl: varchar('action_url', { length: 500 }),
	createdAt: timestamp('created_at').defaultNow().notNull(),
	readAt: timestamp('read_at')
});

export const webhooks = pgTable('webhooks', {
	id: varchar('id', { length: 128 }).primaryKey().$defaultFn(() => createId()),
	organizationId: varchar('organization_id', { length: 128 }).notNull().references(() => organizations.id, { onDelete: 'cascade' }),
	url: varchar('url', { length: 500 }).notNull(),
	events: json('events').notNull(), // array of event types
	secret: varchar('secret', { length: 255 }),
	isActive: boolean('is_active').default(true).notNull(),
	createdBy: varchar('created_by', { length: 128 }).notNull().references(() => users.id),
	createdAt: timestamp('created_at').defaultNow().notNull(),
	updatedAt: timestamp('updated_at').defaultNow().notNull()
});

// Export type inferences for TypeScript
export type User = typeof users.$inferSelect;
export type NewUser = typeof users.$inferInsert;
export type Account = typeof accounts.$inferSelect;
export type Session = typeof sessions.$inferSelect;
export type Organization = typeof organizations.$inferSelect;
export type NewOrganization = typeof organizations.$inferInsert;
export type OrganizationMember = typeof organizationMembers.$inferSelect;
export type Subscription = typeof subscriptions.$inferSelect;
export type Invoice = typeof invoices.$inferSelect;
export type UsageRecord = typeof usageRecords.$inferSelect;
export type ApiKey = typeof apiKeys.$inferSelect;
export type AuditLog = typeof auditLogs.$inferSelect;
export type Notification = typeof notifications.$inferSelect;
export type Webhook = typeof webhooks.$inferSelect;
