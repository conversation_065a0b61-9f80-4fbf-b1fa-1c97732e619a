import { betterAuth } from "better-auth";
import { drizzleAdapter } from "better-auth/adapters/drizzle";
import { db } from "./db";
import { users, accounts, sessions, verificationTokens } from "./db/schema";
import { env, features } from "./env";

export const auth = betterAuth({
	database: drizzleAdapter(db, {
		provider: "pg",
		schema: {
			user: users,
			account: accounts,
			session: sessions,
			verification: verificationTokens
		}
	}),
	baseURL: "http://localhost:5173",
	secret: env.AUTH_SECRET,
	session: {
		expiresIn: 60 * 60 * 24 * 7, // 7 days
		updateAge: 60 * 60 * 24 // 1 day
	},
	emailAndPassword: {
		enabled: true,
		minPasswordLength: 8,
		maxPasswordLength: 128,
		forgotPassword: {
			enabled: true,
			expiresIn: 60 * 60 * 2, // 2 hours
			sendResetPassword: async ({ user, url }) => {
				// TODO: Integrate with email service (Resend/SendGrid)
				console.log(`Password reset for ${user.email}: ${url}`);
			}
		}
	},
	socialProviders: {
		...(features.github && {
			github: {
				clientId: env.GITHUB_CLIENT_ID!,
				clientSecret: env.GITHUB_CLIENT_SECRET!,
			}
		}),
		google: {
			clientId: env.GOOGLE_CLIENT_ID!,
			clientSecret: env.GOOGLE_CLIENT_SECRET!,
		}
	}
});

export type Session = typeof auth.$Infer.Session;