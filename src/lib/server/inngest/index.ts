import { Inngest } from 'inngest';
import { env, features } from '../env';

// Create Inngest client
export const inngest = new Inngest({
	id: 'sveltekit-buildings',
	eventKey: features.inngest ? env.INNGEST_EVENT_KEY : undefined,
	isDev: env.APP_ENV === 'development'
});

// Event type definitions
export type Events = {
	'user/created': {
		data: {
			userId: string;
			email: string;
			name?: string;
		};
	};
	'user/email-verified': {
		data: {
			userId: string;
			email: string;
		};
	};
	'organization/created': {
		data: {
			organizationId: string;
			ownerId: string;
			name: string;
		};
	};
	'subscription/created': {
		data: {
			subscriptionId: string;
			organizationId: string;
			plan: string;
		};
	};
	'subscription/canceled': {
		data: {
			subscriptionId: string;
			organizationId: string;
		};
	};
	'invoice/paid': {
		data: {
			invoiceId: string;
			organizationId: string;
			amount: number;
		};
	};
	'invoice/failed': {
		data: {
			invoiceId: string;
			organizationId: string;
			amount: number;
			reason?: string;
		};
	};
	'usage/threshold-reached': {
		data: {
			organizationId: string;
			feature: string;
			current: number;
			limit: number;
			percentage: number;
		};
	};
};