import { inngest } from './index';
import type { Events } from './index';
import { db } from '../db';
import { users, organizations, notifications, auditLogs } from '../db/schema';
import { eq } from 'drizzle-orm';

// Welcome email function - sends welcome email when user is created
export const sendWelcomeEmail = inngest.createFunction(
	{ id: 'send-welcome-email' },
	{ event: 'user/created' },
	async ({ event, step }) => {
		const { userId, email, name } = event.data;

		await step.run('log-welcome-email', async () => {
			console.log(`Sending welcome email to ${email} (${userId})`);
			
			// In production, integrate with email service (Resend, SendGrid, etc.)
			// For now, just log the action
			await db.insert(auditLogs).values({
				userId,
				action: 'send_welcome_email',
				resource: 'email',
				resourceId: `welcome_${userId}`,
				metadata: { email, recipientName: name }
			});
		});

		await step.run('create-notification', async () => {
			// Create in-app notification
			await db.insert(notifications).values({
				userId,
				title: 'Welcome to Buildings App!',
				message: `Hi ${name || 'there'}! Welcome to Buildings App. We're excited to have you on board.`,
				type: 'info',
				actionUrl: '/dashboard'
			});
		});

		return { success: true };
	}
);

// Email verification function
export const sendEmailVerification = inngest.createFunction(
	{ id: 'send-email-verification' },
	{ event: 'user/email-verified' },
	async ({ event, step }) => {
		const { userId, email } = event.data;

		await step.run('create-verification-notification', async () => {
			await db.insert(notifications).values({
				userId,
				title: 'Email Verified',
				message: 'Your email has been successfully verified. You now have full access to all features.',
				type: 'success'
			});
		});

		await step.run('log-verification', async () => {
			await db.insert(auditLogs).values({
				userId,
				action: 'email_verified',
				resource: 'user',
				resourceId: userId,
				metadata: { email }
			});
		});

		return { success: true };
	}
);

// Organization setup function
export const setupNewOrganization = inngest.createFunction(
	{ id: 'setup-new-organization' },
	{ event: 'organization/created' },
	async ({ event, step }) => {
		const { organizationId, ownerId, name } = event.data;

		await step.run('create-welcome-notification', async () => {
			await db.insert(notifications).values({
				userId: ownerId,
				title: 'Organization Created',
				message: `Your organization "${name}" has been successfully created. You can now invite team members and start collaborating.`,
				type: 'success',
				actionUrl: `/organizations/${organizationId}`
			});
		});

		await step.run('send-setup-email', async () => {
			// In production, send organization setup guide email
			console.log(`Sending organization setup guide to organization ${organizationId}`);
			
			await db.insert(auditLogs).values({
				userId: ownerId,
				action: 'send_org_setup_guide',
				resource: 'email',
				resourceId: `org_setup_${organizationId}`,
				metadata: { organizationId, organizationName: name }
			});
		});

		return { success: true };
	}
);

// Subscription management functions
export const handleSubscriptionCreated = inngest.createFunction(
	{ id: 'handle-subscription-created' },
	{ event: 'subscription/created' },
	async ({ event, step }) => {
		const { subscriptionId, organizationId, plan } = event.data;

		// Get organization owner
		const org = await step.run('get-organization', async () => {
			const result = await db
				.select({ ownerId: organizations.ownerId, name: organizations.name })
				.from(organizations)
				.where(eq(organizations.id, organizationId))
				.limit(1);
			return result[0];
		});

		if (!org) {
			throw new Error('Organization not found');
		}

		await step.run('create-subscription-notification', async () => {
			await db.insert(notifications).values({
				userId: org.ownerId,
				title: 'Subscription Activated',
				message: `Your ${plan} subscription has been activated for "${org.name}". You now have access to all premium features.`,
				type: 'success',
				actionUrl: `/organizations/${organizationId}/billing`
			});
		});

		await step.run('log-subscription-activation', async () => {
			await db.insert(auditLogs).values({
				userId: org.ownerId,
				action: 'subscription_activated',
				resource: 'subscription',
				resourceId: subscriptionId,
				metadata: { organizationId, plan, organizationName: org.name }
			});
		});

		return { success: true };
	}
);

// Handle subscription cancellation
export const handleSubscriptionCanceled = inngest.createFunction(
	{ id: 'handle-subscription-canceled' },
	{ event: 'subscription/canceled' },
	async ({ event, step }) => {
		const { subscriptionId, organizationId } = event.data;

		// Get organization owner
		const org = await step.run('get-organization', async () => {
			const result = await db
				.select({ ownerId: organizations.ownerId, name: organizations.name })
				.from(organizations)
				.where(eq(organizations.id, organizationId))
				.limit(1);
			return result[0];
		});

		if (!org) {
			throw new Error('Organization not found');
		}

		await step.run('create-cancellation-notification', async () => {
			await db.insert(notifications).values({
				userId: org.ownerId,
				title: 'Subscription Canceled',
				message: `Your subscription for "${org.name}" has been canceled. You'll continue to have access until the end of your current billing period.`,
				type: 'warning',
				actionUrl: `/organizations/${organizationId}/billing`
			});
		});

		await step.run('schedule-feedback-request', async () => {
			// Schedule a feedback request email for 1 day later
			await inngest.send({
				name: 'user/feedback-request',
				data: {
					userId: org.ownerId,
					organizationId,
					reason: 'subscription_canceled'
				}
			});
		});

		return { success: true };
	}
);

// Usage threshold monitoring
export const handleUsageThreshold = inngest.createFunction(
	{ id: 'handle-usage-threshold' },
	{ event: 'usage/threshold-reached' },
	async ({ event, step }) => {
		const { organizationId, feature, current, limit, percentage } = event.data;

		// Get organization owner
		const org = await step.run('get-organization', async () => {
			const result = await db
				.select({ ownerId: organizations.ownerId, name: organizations.name })
				.from(organizations)
				.where(eq(organizations.id, organizationId))
				.limit(1);
			return result[0];
		});

		if (!org) {
			throw new Error('Organization not found');
		}

		const thresholdType = percentage >= 90 ? 'critical' : percentage >= 75 ? 'warning' : 'info';
		
		await step.run('create-usage-notification', async () => {
			await db.insert(notifications).values({
				userId: org.ownerId,
				title: `${feature} Usage Alert`,
				message: `Your organization "${org.name}" has used ${percentage.toFixed(1)}% of your ${feature} limit (${current} of ${limit}).`,
				type: thresholdType === 'critical' ? 'error' : thresholdType,
				actionUrl: `/organizations/${organizationId}/billing`
			});
		});

		// Send email alert for critical usage
		if (thresholdType === 'critical') {
			await step.run('send-critical-usage-email', async () => {
				console.log(`Sending critical usage alert email for ${feature} to organization ${organizationId}`);
				
				await db.insert(auditLogs).values({
					userId: org.ownerId,
					action: 'send_usage_alert',
					resource: 'email',
					resourceId: `usage_alert_${organizationId}_${feature}`,
					metadata: { organizationId, feature, current, limit, percentage }
				});
			});
		}

		return { success: true };
	}
);

// Data cleanup functions
export const cleanupExpiredSessions = inngest.createFunction(
	{ id: 'cleanup-expired-sessions' },
	{ cron: '0 2 * * *' }, // Run daily at 2 AM
	async ({ step }) => {
		await step.run('remove-expired-sessions', async () => {
			// In production, this would clean up expired sessions
			console.log('Cleaning up expired sessions...');
			
			// Example cleanup (implement based on your session management)
			const expiredDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000); // 7 days ago
			
			// Clean up expired verification tokens, old audit logs, etc.
			await db.insert(auditLogs).values({
				action: 'cleanup_expired_sessions',
				resource: 'system',
				resourceId: `cleanup_${Date.now()}`,
				metadata: { cleanupDate: expiredDate.toISOString() }
			});
		});

		return { success: true };
	}
);

// Generate monthly reports
export const generateMonthlyReports = inngest.createFunction(
	{ id: 'generate-monthly-reports' },
	{ cron: '0 0 1 * *' }, // Run on the 1st of every month at midnight
	async ({ step }) => {
		await step.run('generate-usage-reports', async () => {
			console.log('Generating monthly usage reports...');
			
			// In production, this would generate comprehensive usage reports
			// and send them to organization owners
			
			await db.insert(auditLogs).values({
				action: 'generate_monthly_reports',
				resource: 'system',
				resourceId: `monthly_report_${Date.now()}`,
				metadata: { reportMonth: new Date().toISOString().slice(0, 7) }
			});
		});

		return { success: true };
	}
);

// Export all functions
export const inngestFunctions = [
	sendWelcomeEmail,
	sendEmailVerification,
	setupNewOrganization,
	handleSubscriptionCreated,
	handleSubscriptionCanceled,
	handleUsageThreshold,
	cleanupExpiredSessions,
	generateMonthlyReports
];