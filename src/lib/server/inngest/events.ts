import { inngest } from './index';
import type { Events } from './index';
import { features } from '../env';

// Helper function to safely send events only if Inngest is configured
async function sendEvent<T extends keyof Events>(
	name: T,
	data: Events[T]['data']
): Promise<void> {
	if (!features.inngest) {
		console.warn(`Inngest not configured, skipping event: ${name}`);
		return;
	}

	try {
		await inngest.send({
			name,
			data
		});
		console.log(`Sent Inngest event: ${name}`);
	} catch (error) {
		console.error(`Failed to send Inngest event ${name}:`, error);
	}
}

// Event trigger functions
export const triggerUserCreated = (data: Events['user/created']['data']) =>
	sendEvent('user/created', data);

export const triggerEmailVerified = (data: Events['user/email-verified']['data']) =>
	sendEvent('user/email-verified', data);

export const triggerOrganizationCreated = (data: Events['organization/created']['data']) =>
	sendEvent('organization/created', data);

export const triggerSubscriptionCreated = (data: Events['subscription/created']['data']) =>
	sendEvent('subscription/created', data);

export const triggerSubscriptionCanceled = (data: Events['subscription/canceled']['data']) =>
	sendEvent('subscription/canceled', data);

export const triggerInvoicePaid = (data: Events['invoice/paid']['data']) =>
	sendEvent('invoice/paid', data);

export const triggerInvoiceFailed = (data: Events['invoice/failed']['data']) =>
	sendEvent('invoice/failed', data);

export const triggerUsageThreshold = (data: Events['usage/threshold-reached']['data']) =>
	sendEvent('usage/threshold-reached', data);

// Batch event sending for multiple events
export async function sendEvents<T extends keyof Events>(
	events: Array<{ name: T; data: Events[T]['data'] }>
): Promise<void> {
	if (!features.inngest) {
		console.warn('Inngest not configured, skipping batch events');
		return;
	}

	try {
		await inngest.send(events.map(event => ({
			name: event.name,
			data: event.data
		})));
		console.log(`Sent ${events.length} Inngest events in batch`);
	} catch (error) {
		console.error('Failed to send batch Inngest events:', error);
	}
}