import { Ratelimit } from '@upstash/ratelimit';
import { Redis } from '@upstash/redis';
import { env, features } from './env';

// In-memory store for rate limiting when Redis is not available
class MemoryStore {
	private store = new Map<string, { count: number; reset: number }>();

	async get(key: string): Promise<{ count: number; reset: number } | null> {
		const item = this.store.get(key);
		if (!item) return null;
		
		// Clean up expired entries
		if (Date.now() > item.reset) {
			this.store.delete(key);
			return null;
		}
		
		return item;
	}

	async set(key: string, value: { count: number; reset: number }): Promise<void> {
		this.store.set(key, value);
	}

	async incr(key: string, windowMs: number): Promise<number> {
		const now = Date.now();
		const item = this.store.get(key);
		
		if (!item || now > item.reset) {
			const newItem = { count: 1, reset: now + windowMs };
			this.store.set(key, newItem);
			return 1;
		}
		
		item.count++;
		this.store.set(key, item);
		return item.count;
	}
}

// Create Redis instance if available, otherwise use in-memory store
const redis = features.redis 
	? new Redis({
		url: env.REDIS_URL!,
		automaticDeserialization: false
	})
	: null;

// Create different rate limiters for different use cases
export const rateLimiters = {
	// General API rate limit: 100 requests per minute
	api: new Ratelimit({
		redis: redis || new MemoryStore() as any,
		limiter: Ratelimit.slidingWindow(100, '1 m'),
		analytics: true,
		prefix: 'ratelimit:api'
	}),
	
	// Authentication rate limit: 5 attempts per minute
	auth: new Ratelimit({
		redis: redis || new MemoryStore() as any,
		limiter: Ratelimit.slidingWindow(5, '1 m'),
		analytics: true,
		prefix: 'ratelimit:auth'
	}),
	
	// Password reset: 3 attempts per hour
	passwordReset: new Ratelimit({
		redis: redis || new MemoryStore() as any,
		limiter: Ratelimit.slidingWindow(3, '1 h'),
		analytics: true,
		prefix: 'ratelimit:password-reset'
	}),
	
	// Email sending: 10 emails per hour
	email: new Ratelimit({
		redis: redis || new MemoryStore() as any,
		limiter: Ratelimit.slidingWindow(10, '1 h'),
		analytics: true,
		prefix: 'ratelimit:email'
	}),
	
	// AI API calls: 20 per minute
	ai: new Ratelimit({
		redis: redis || new MemoryStore() as any,
		limiter: Ratelimit.slidingWindow(20, '1 m'),
		analytics: true,
		prefix: 'ratelimit:ai'
	})
};

// Helper function to get client identifier
export function getClientId(request: Request): string {
	// Try to get IP address from various headers
	const forwarded = request.headers.get('x-forwarded-for');
	const realIp = request.headers.get('x-real-ip');
	const cfConnectingIp = request.headers.get('cf-connecting-ip');
	
	const ip = forwarded?.split(',')[0] || realIp || cfConnectingIp || 'unknown';
	
	// For authenticated requests, you might want to use user ID instead
	// This would need to be implemented based on your auth system
	return ip;
}

// tRPC middleware helper
export async function rateLimitMiddleware(
	rateLimiter: Ratelimit,
	identifier: string
) {
	const { success, limit, reset, remaining } = await rateLimiter.limit(identifier);
	
	if (!success) {
		throw new Error(`Rate limit exceeded. Try again in ${Math.round((reset - Date.now()) / 1000)} seconds.`);
	}
	
	return {
		success,
		limit,
		reset,
		remaining
	};
}