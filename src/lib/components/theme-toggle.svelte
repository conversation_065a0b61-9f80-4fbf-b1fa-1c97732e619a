<script lang="ts">
	import { setMode, mode } from 'mode-watcher';
	import { But<PERSON> } from '$lib/components/ui/button/index.js';
	import Sun from 'lucide-svelte/icons/sun';
	import Moon from 'lucide-svelte/icons/moon';

	function toggleMode() {
		setMode(mode.current === 'dark' ? 'light' : 'dark');
	}
</script>

<Button onclick={toggleMode} variant="outline" size="icon">
	{#if mode.current === 'dark'}
		<Sun class="h-[1.2rem] w-[1.2rem]" />
		<span class="sr-only">Switch to light mode</span>
	{:else}
		<Moon class="h-[1.2rem] w-[1.2rem]" />
		<span class="sr-only">Switch to dark mode</span>
	{/if}
</Button> 