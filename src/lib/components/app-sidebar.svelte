<script lang="ts" module>
	import AudioWaveformIcon from "@lucide/svelte/icons/audio-waveform";
	import BookOpenIcon from "@lucide/svelte/icons/book-open";
	import BotIcon from "@lucide/svelte/icons/bot";
	import ChartPieIcon from "@lucide/svelte/icons/chart-pie";
	import CommandIcon from "@lucide/svelte/icons/command";
	import FrameIcon from "@lucide/svelte/icons/frame";
	import GalleryVerticalEndIcon from "@lucide/svelte/icons/gallery-vertical-end";
	import MapIcon from "@lucide/svelte/icons/map";
	import Settings2Icon from "@lucide/svelte/icons/settings-2";
	import SquareTerminalIcon from "@lucide/svelte/icons/square-terminal";

	import BuildingIcon from "@lucide/svelte/icons/building";
	import CreditCardIcon from "@lucide/svelte/icons/credit-card";
	import HomeIcon from "@lucide/svelte/icons/home";
	import UsersIcon from "@lucide/svelte/icons/users";
	import UserIcon from "@lucide/svelte/icons/user";

	// SaaS application navigation data
	const data = {
		user: {
			name: "User",
			email: "<EMAIL>",
			avatar: "",
		},
		teams: [
			{
				name: "Personal",
				logo: UserIcon,
				plan: "Free",
			},
		],
		navMain: [
			{
				title: "Dashboard",
				url: "/",
				icon: HomeIcon,
				isActive: true,
			},
			{
				title: "Organizations",
				url: "/organizations",
				icon: BuildingIcon,
				items: [
					{
						title: "My Organizations",
						url: "/organizations",
					},
					{
						title: "Create New",
						url: "/organizations#create",
					},
				],
			},
			{
				title: "Billing",
				url: "/billing",
				icon: CreditCardIcon,
				items: [
					{
						title: "Subscriptions",
						url: "/billing/subscriptions",
					},
					{
						title: "Invoices",
						url: "/billing/invoices",
					},
					{
						title: "Usage",
						url: "/billing/usage",
					},
				],
			},
			{
				title: "Account",
				url: "/account",
				icon: UserIcon,
				items: [
					{
						title: "Profile",
						url: "/account/profile",
					},
					{
						title: "Security",
						url: "/account/security",
					},
					{
						title: "Notifications",
						url: "/account/notifications",
					},
				],
			},
		],
		projects: [
			{
				name: "User Management",
				url: "/admin/users",
				icon: UsersIcon,
			},
			{
				name: "System Monitoring",
				url: "/admin/system",
				icon: Settings2Icon,
			},
			{
				name: "Activity Logs",
				url: "/admin/logs",
				icon: BookOpenIcon,
			},
		],
	};
</script>

<script lang="ts">
	import NavMain from "./nav-main.svelte";
	import NavProjects from "./nav-projects.svelte";
	import NavUser from "./nav-user.svelte";
	import TeamSwitcher from "./team-switcher.svelte";
	import * as Sidebar from "$lib/components/ui/sidebar/index.js";
	import type { ComponentProps } from "svelte";

	let {
		ref = $bindable(null),
		collapsible = "icon",
		...restProps
	}: ComponentProps<typeof Sidebar.Root> = $props();
</script>

<Sidebar.Root {collapsible} {...restProps}>
	<Sidebar.Header>
		<TeamSwitcher teams={data.teams} />
	</Sidebar.Header>
	<Sidebar.Content>
		<NavMain items={data.navMain} />
		<NavProjects projects={data.projects} />
	</Sidebar.Content>
	<Sidebar.Footer>
		<NavUser user={data.user} />
	</Sidebar.Footer>
	<Sidebar.Rail />
</Sidebar.Root>
