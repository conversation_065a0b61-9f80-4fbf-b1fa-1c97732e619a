import { createTRPC<PERSON>lient, type TRPCClientInit } from 'trpc-sveltekit';
import type { AppRouter } from '$lib/server/trpc/root';
import { browser } from '$app/environment';

let browserClient: ReturnType<typeof createTRPCClient<AppRouter>>;

function getUrl(): `/${string}` {
	if (browser) return '/api/trpc';
	return '/api/trpc';
}

export function trpc(init?: TRPCClientInit) {
	const isBrowser = typeof window !== 'undefined';
	if (isBrowser && browserClient) return browserClient;
	
	const client = createTRPCClient<AppRouter>({
		url: getUrl(),
		...init
	});
	
	if (isBrowser) browserClient = client;
	return client;
}