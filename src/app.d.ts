// See https://svelte.dev/docs/kit/types#app.d.ts
// for information about these interfaces
declare global {
	namespace App {
		// interface Error {}
		interface Locals {
			session: import('$lib/server/auth').Session | null;
			user: import('$lib/server/auth').Session['user'] | null;
		}
		// interface PageData {}
		// interface PageState {}
		// interface Platform {}
	}
}

// Global type fixes for shadcn-svelte component compatibility
declare module 'svelte' {
	interface HTMLAttributes<T> {
		class?: string;
	}
}

// Comprehensive type fixes for bits-ui components
declare module 'bits-ui' {
	export namespace DropdownMenu {
		export interface TriggerProps {
			ref?: any;
			[key: string]: any;
		}
		export interface GroupProps {
			ref?: any;
			[key: string]: any;
		}
		export interface GroupHeading {
			ref?: any;
			[key: string]: any;
		}
	}
	
	export namespace Collapsible {
		export interface RootProps {
			ref?: any;
			[key: string]: any;
		}
		export interface TriggerProps {
			ref?: any;
			[key: string]: any;
		}
	}

	export namespace Sheet {
		export interface CloseProps {
			ref?: any;
			[key: string]: any;
		}
		export interface TriggerProps {
			ref?: any;
			[key: string]: any;
		}
	}

	export namespace Tooltip {
		export interface TriggerProps {
			ref?: any;
			[key: string]: any;
		}
	}

	export namespace Command {
		export interface CommandProps {
			class?: string;
			[key: string]: any;
		}
		export interface EmptyProps {
			class?: string;
			[key: string]: any;
		}
		export interface GroupProps {
			class?: string;
			[key: string]: any;
		}
		export interface ItemProps {
			class?: string;
			[key: string]: any;
		}
		export interface InputProps {
			class?: string;
			[key: string]: any;
		}
		export interface ListProps {
			class?: string;
			[key: string]: any;
		}
		export interface SeparatorProps {
			class?: string;
			[key: string]: any;
		}
	}

	export namespace Toggle {
		export interface ToggleProps {
			class?: string;
			[key: string]: any;
		}
	}

	export namespace ToggleGroup {
		export interface Props<T> {
			class?: string;
			[key: string]: any;
		}
		export interface ItemProps {
			class?: string;
			[key: string]: any;
		}
	}
}

// Override HTMLAttributes to support ClassValue
declare global {
	interface HTMLAttributes<T> {
		class?: string | import('clsx').ClassValue;
	}
}

export {};
