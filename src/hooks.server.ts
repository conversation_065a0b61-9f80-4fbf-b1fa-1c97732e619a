import { auth } from "$lib/server/auth";
import { sequence } from "@sveltejs/kit/hooks";
import type { Handle } from "@sveltejs/kit";

const handleAuth: Handle = async ({ event, resolve }) => {
	// Add auth session to locals
	const session = await auth.api.getSession({
		headers: event.request.headers
	});
	
	event.locals.session = session;
	event.locals.user = session?.user;
	
	return await resolve(event);
};

const handleCustom: Handle = async ({ event, resolve }) => {
	// Add custom logic here if needed
	// For example, rate limiting, logging, etc.
	
	return await resolve(event);
};

export const handle = sequence(handleAuth, handleCustom);